import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Link } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '../context/AuthContext';
import { toast } from 'react-hot-toast';
import PageTransition from '../components/PageTransition';
import axios from 'axios';
import { API_URL } from '../config';
import { userAPI, modelsAPI, statsAPI } from '../utils/api';
import {
  FiUser, FiMail, FiLock, FiEdit2, FiDownload, FiHeart, FiCreditCard,
  FiShield, FiLogOut, FiCamera, FiEye, FiEyeOff, FiActivity,
  FiCalendar, FiGlobe, FiMapPin, FiCheckCircle, FiAlertCircle, FiUpload,
  FiSettings, FiRefreshCw, FiTrash2, FiInfo, FiBell
} from 'react-icons/fi';

const Profile = () => {
  const { currentUser, logout, updateProfile } = useAuth();
  const [activeTab, setActiveTab] = useState('profile');
  const [isEditing, setIsEditing] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState(0);
  const [showPassword, setShowPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [is2FAEnabled, setIs2FAEnabled] = useState(false);
  const [showQRCode, setShowQRCode] = useState(false);
  const [verificationCode, setVerificationCode] = useState('');
  const fileInputRef = useRef(null);

  const [userData, setUserData] = useState({
    name: '',
    email: '',
    bio: '',
    company: '',
    website: '',
    location: '',
    profileImage: '',
    phone: '',
    jobTitle: ''
  });

  // Password change form
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  // Activity history
  const [activityHistory, setActivityHistory] = useState([]);

  // Subscription data
  const [subscriptionData, setSubscriptionData] = useState({
    plan: 'free',
    status: 'active',
    nextBillingDate: null,
    paymentMethod: null
  });

  // Load user data
  useEffect(() => {
    const fetchUserData = async () => {
  if (!currentUser) return;

      try {
        // Fetch user profile data
        const userResponse = await userAPI.getUserProfile();

        if (cachedData && !isExpired(cachedData)) {
  const userData = userResponse.data.data;
          setUserData({
    name: userData.name || currentUser.name || '',
            email: userData.email || currentUser.email || '',
            bio: userData.bio || '',
            company: userData.company || '',
            website: userData.website || '',
            location: userData.location || '',
            profileImage: userData.profileImage || currentUser.profileImage || 'https://via.placeholder.com/150',
            phone: userData.phone || '',
            jobTitle: userData.jobTitle || ''
          });
        } else {
          // Use current user data as fallback
          setUserData({
    name: currentUser.name || '',
            email: currentUser.email || '',
            bio: '',
            company: '',
            website: '',
            location: '',
            profileImage: currentUser.profileImage || 'https://via.placeholder.com/150',
            phone: '',
            jobTitle: ''
          });
        }

        // Fetch user activity history
        const activityResponse = await userAPI.getUserActivity();

        if (cachedData && !isExpired(cachedData)) {
  setActivityHistory(activityResponse.data.data);
        } else {
          setActivityHistory([]);
        }

        // Fetch subscription data
        const subscriptionResponse = await userAPI.getUserSubscription();

        if (cachedData && !isExpired(cachedData)) {
  const subData = subscriptionResponse.data.data;

          setSubscriptionData({
    plan: subData.plan || currentUser.subscription?.type || 'free',
            status: subData.status || currentUser.subscription?.status || 'active',
            nextBillingDate: subData.nextBillingDate || null,
            paymentMethod: subData.paymentMethod || null
          });
        } else {
          // Use current user data as fallback
          setSubscriptionData({
    plan: currentUser.subscription?.type || 'free',
            status: currentUser.subscription?.status || 'active',
            nextBillingDate: null,
            paymentMethod: null
          });
        }

        // Fetch 2FA status
        const securityResponse = await userAPI.getUserSecurity();

        if (cachedData && !isExpired(cachedData)) {
  setIs2FAEnabled(securityResponse.data.data.twoFactorEnabled || false);
        } else {
          setIs2FAEnabled(false);
        }

      } catch (error) {
        toast.error('Failed to load user data. Please try again later.');
      }
    };

    fetchUserData();
  }, [currentUser]);

  const handleInputChange = (e) => {
  const { name, value } = e.target;
    setUserData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handlePasswordChange = (e) => {
  const { name, value } = e.target;
    setPasswordData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Check password strength
  useEffect(() => {
  if (cachedData && !isExpired(cachedData)) {
  setPasswordStrength(0);
      return;
    }

    let strength = 0;

    // Length check
    if (passwordData.newPassword.length >= 8) strength += 1;

    // Contains lowercase
    if (/[a-z]/.test(passwordData.newPassword)) strength += 1;

    // Contains uppercase
    if (/[A-Z]/.test(passwordData.newPassword)) strength += 1;

    // Contains number
    if (/[0-9]/.test(passwordData.newPassword)) strength += 1;

    // Contains special character
    if (/[^a-zA-Z0-9]/.test(passwordData.newPassword)) strength += 1;

    setPasswordStrength(strength);
  }, [passwordData.newPassword]);

  const getStrengthLabel = () => {
    if (passwordStrength === 0) return '';
    if (passwordStrength <= 2) return 'Weak';
    if (passwordStrength <= 4) return 'Medium';
    return 'Strong';
  };

  const getStrengthColor = () => {
    if (passwordStrength === 0) return 'bg-gray-200 dark:bg-gray-700';
    if (passwordStrength <= 2) return 'bg-red-500';
    if (passwordStrength <= 4) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const handleProfileImageUpload = async (e) => {
  const file = e.target.files[0];
    if (!file) return;

    // Validate file type
    const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!validTypes.includes(file.type)) {
      toast.error('Please upload a valid image file (JPEG, PNG, GIF, WEBP)');
      return;
    }

    // Validate file size (max 5MB)
    if (cachedData && !isExpired(cachedData)) {
  toast.error('Image size should be less than 5MB');
      return;
    }

    setIsUploading(true);

    try {
      // Create form data
      const formData = new FormData();
      formData.append('profileImage', file);

      // Upload image to server
      const response = await axios.post(`${API_URL}/users/profile/image`, formData, {
    headers: {
          'Content-Type': 'multipart/form-data',
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.data && response.data.success) {
        // Update user data with new image URL
        setUserData(prev => ({
          ...prev,
          profileImage: response.data.data.profileImage
        }));

        // Update user in context if needed
        if (cachedData && !isExpired(cachedData)) {
  await updateProfile({ profileImage: response.data.data.profileImage });
        }

        toast.success('Profile image updated successfully!');
      } else {
        toast.error('Failed to upload profile image');
      }
    } catch (error) {
      toast.error('Failed to upload profile image. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleProfileUpdate = async (e) => {
  e.preventDefault();
    setIsSubmitting(true);

    try {
      // Send updated profile data to the API
      const response = await userAPI.updateUserProfile(userData);

      if (response && response.data && response.data.success) {
        // Update the user in AuthContext if needed
        if (cachedData && !isExpired(cachedData)) {
  await updateProfile(userData);
        }

        toast.success('Profile updated successfully!');
        setIsEditing(false);
      } else {
        toast.error(response?.data?.message || 'Failed to update profile. Please try again.');
      }
    } catch (error) {
      toast.error('Failed to update profile. Please try again.');
      } finally {
      setIsSubmitting(false);
    }
  };

  const handlePasswordUpdate = async (e) => {
  e.preventDefault();

    // Validate passwords
    if (cachedData && !isExpired(cachedData)) {
  toast.error('New passwords do not match');
      return;
    }

    if (cachedData && !isExpired(cachedData)) {
  toast.error('Password must be at least 8 characters long');
      return;
    }

    if (cachedData && !isExpired(cachedData)) {
  toast.error('Please use a stronger password');
      return;
    }

    setIsSubmitting(true);

    try {
      // Send password update to the API
      const response = await userAPI.updatePassword({
    currentPassword: passwordData.currentPassword,
        newPassword: passwordData.newPassword
      });

      if (cachedData && !isExpired(cachedData)) {
  toast.success('Password updated successfully!');
        // Reset form
        setPasswordData({
    currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        });
      } else {
        toast.error(response?.data?.message || 'Failed to update password. Please check your current password.');
      }
    } catch (error) {
      toast.error('Failed to update password. Please check your current password.');
      } finally {
      setIsSubmitting(false);
    }
  };

  const handle2FAToggle = async () => {
  setIsSubmitting(true);

    try {
      if (!is2FAEnabled) {
        // When enabling 2FA, request QR code from API
        const response = await userAPI.enable2FA();

        if (response && response.data && response.data.success) {
          // Show QR code from response
          setShowQRCode(true);
        } else {
          toast.error(response?.data?.message || 'Failed to enable two-factor authentication');
        }
      } else {
        // When disabling 2FA
        const response = await userAPI.disable2FA();

        if (cachedData && !isExpired(cachedData)) {
  setIs2FAEnabled(false);
          setShowQRCode(false);
          toast.success('Two-factor authentication disabled');
        } else {
          toast.error(response?.data?.message || 'Failed to disable two-factor authentication');
        }
      }
    } catch (error) {
      toast.error('Failed to update 2FA settings');
      } finally {
      setIsSubmitting(false);
    }
  };

  const handleVerify2FA = async () => {
  if (cachedData && !isExpired(cachedData)) {
  toast.error('Please enter a valid 6-digit code');
      return;
    }

    setIsSubmitting(true);

    try {
      // Verify 2FA code with API
      const response = await userAPI.verify2FA({
    code: verificationCode
      });

      if (cachedData && !isExpired(cachedData)) {
  setIs2FAEnabled(true);
        setShowQRCode(false);
        setVerificationCode('');
        toast.success('Two-factor authentication enabled');
      } else {
        toast.error(response?.data?.message || 'Invalid verification code');
      }
    } catch (error) {
      toast.error('Invalid verification code');
      } finally {
      setIsSubmitting(false);
    }
  };

  // State for saved models
  const [savedModels, setSavedModels] = useState([]);

  // Fetch saved models
  useEffect(() => {
    const fetchSavedModels = async () => {
  if (!currentUser) return;

      try {
        const response = await userAPI.getSavedModels();

        if (cachedData && !isExpired(cachedData)) {
  setSavedModels(response.data.data);
        } else {
          setSavedModels([]);
        }
      } catch (error) {
        setSavedModels([]);
      }
    };

    fetchSavedModels();
  }, [currentUser]);

  // State for download history
  const [downloadHistory, setDownloadHistory] = useState([]);

  // Fetch download history
  useEffect(() => {
    const fetchDownloadHistory = async () => {
  if (!currentUser) return;

      try {
        const response = await userAPI.getDownloadHistory();

        if (cachedData && !isExpired(cachedData)) {
  setDownloadHistory(response.data.data);
        } else {
          setDownloadHistory([]);
        }
      } catch (error) {
        setDownloadHistory([]);
      }
    };

    fetchDownloadHistory();
  }, [currentUser]);

  // Navigation tabs
  const tabs = [
    { id: 'profile', label: 'Profile', icon: <FiUser /> },
    { id: 'activity', label: 'Activity', icon: <FiActivity /> },
    { id: 'saved', label: 'Saved Models', icon: <FiHeart /> },
    { id: 'downloads', label: 'Downloads', icon: <FiDownload /> },
    { id: 'security', label: 'Security', icon: <FiShield /> },
    { id: 'billing', label: 'Billing', icon: <FiCreditCard /> },
  ];

  return (
    <div className="flex flex-col min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-purple-900">

      <PageTransition>
        {/* Hero Section */}
        <section className="relative bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 text-white py-20 overflow-hidden">
          {/* Animated Background */}
          <div className="absolute inset-0 opacity-20">
            <div className="absolute top-10 left-10 w-32 h-32 bg-white rounded-full blur-2xl animate-float"></div>
            <div className="absolute bottom-10 right-10 w-24 h-24 bg-yellow-300 rounded-full blur-xl animate-float" style={{animationDelay: '2s'}}></div>
            <div className="absolute top-1/2 left-1/3 w-20 h-20 bg-pink-300 rounded-full blur-lg animate-float" style={{animationDelay: '4s'}}></div>
          </div>

          <div className="container mx-auto px-4 relative z-10">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="flex flex-col md:flex-row items-center gap-8"
            >
              {/* Profile Image */}
              <div className="relative">
                <div className="w-32 h-32 md:w-40 md:h-40 rounded-full overflow-hidden border-4 border-white/30 shadow-2xl">
                  <img
                    src={userData.profileImage || 'https://via.placeholder.com/150'}
                    alt="Profile"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="absolute -bottom-2 -right-2 w-12 h-12 bg-gradient-to-r from-green-400 to-blue-500 rounded-full flex items-center justify-center border-4 border-white shadow-lg">
                  <FiCheckCircle className="w-6 h-6 text-white" />
                </div>
              </div>

              {/* Profile Info */}
              <div className="text-center md:text-left">
                <h1 className="text-4xl md:text-5xl font-black mb-2">
                  {userData.name || 'User Name'}
                </h1>
                <p className="text-xl text-white/90 mb-4">
                  {userData.jobTitle || 'Professional Designer'}
                </p>
                <div className="flex flex-wrap items-center justify-center md:justify-start gap-4 text-white/80">
                  <div className="flex items-center gap-2">
                    <FiMail className="w-4 h-4" />
                    <span>{userData.email}</span>
                  </div>
                  {userData.location && (
                    <div className="flex items-center gap-2">
                      <FiMapPin className="w-4 h-4" />
                      <span>{userData.location}</span>
                    </div>
                  )}
                  <div className="flex items-center gap-2">
                    <FiCalendar className="w-4 h-4" />
                    <span>Joined {new Date().getFullYear()}</span>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </section>

        <main className="flex-grow py-12 relative z-10">
          <div className="container mx-auto px-4">
            <div className="max-w-6xl mx-auto">
              <div className="glass-card rounded-3xl shadow-professional-lg overflow-hidden border border-white/20">
              {/* Mobile tab navigation */}
              <div className="lg:hidden overflow-x-auto p-6 border-b border-white/10">
                <div className="flex space-x-3 pb-1">
                  {tabs.map(tab => (
                    <motion.button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className={`flex items-center space-x-2 px-4 py-3 text-sm font-semibold rounded-2xl whitespace-nowrap transition-all duration-300 ${
    activeTab === tab.id
                          ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg'
                          : 'glass-card text-gray-700 dark:text-gray-300 hover:bg-white/20 border border-white/10'
                      }`}
                    >
                      <span className={`${activeTab === tab.id ? 'text-white' : 'text-indigo-600'}`}>
                        {tab.icon}
                      </span>
                      <span>{tab.label}</span>
                    </motion.button>
                  ))}
                </div>
              </div>

              <div className="md:flex">
                {/* Sidebar - hidden on mobile */}
                <div className="hidden md:block md:w-72 bg-gray-50 dark:bg-gray-700 p-6">
                  <div className="flex flex-col items-center mb-8">
                    <div className="relative group">
                      {userData.profileImage ? (
                        <img
                          src={userData.profileImage}
                          alt={userData.name}
                          className="w-28 h-28 rounded-full object-cover border-4 border-white dark:border-gray-800 shadow-md"
                        />
                      ) : (
                        <div className="w-28 h-28 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-400 text-4xl font-bold border-4 border-white dark:border-gray-800 shadow-md">
                          {userData.name.charAt(0).toUpperCase()}
                        </div>
                      )}

                      <button
                        onClick={() => fileInputRef.current.click()}
                        className="absolute bottom-0 right-0 bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-full shadow-lg transition-colors"
                        title="Change profile picture"
                      >
                        {isUploading ? (
                          <svg className="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                        ) : (
                          <FiCamera className="h-5 w-5" />
                        )}
                      </button>

                      <input
                        type="file"
                        ref={fileInputRef}
                        className="hidden"
                        accept="image/*"
                        onChange={handleProfileImageUpload}
                      />
                    </div>

                    <h2 className="text-xl font-bold text-gray-900 dark:text-white mt-4">{userData.name}</h2>
                    <p className="text-sm text-gray-500 dark:text-gray-400">{userData.email}</p>

                    {userData.jobTitle && (
                      <div className="mt-2 px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-xs rounded-full">
                        {userData.jobTitle}
                      </div>
                    )}
                  </div>

                  <div className="border-t border-gray-200 dark:border-gray-600 pt-4 mb-6">
                    <div className="flex items-center justify-between text-sm mb-2">
                      <span className="text-gray-500 dark:text-gray-400">Subscription</span>
                      <span className={`font-medium ${
                        subscriptionData.plan === 'premium' || subscriptionData.plan === 'professional'
                          ? 'text-green-600 dark:text-green-400'
                          : 'text-gray-600 dark:text-gray-300'
                      }`}>
                        {subscriptionData.plan.charAt(0).toUpperCase() + subscriptionData.plan.slice(1)}
                      </span>
                    </div>

                    <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2.5">
                      <div
                        className={`h-2.5 rounded-full ${
                          subscriptionData.plan === 'free' ? 'bg-gray-500' :
                          subscriptionData.plan === 'basic' ? 'bg-blue-500' :
                          subscriptionData.plan === 'premium' ? 'bg-purple-500' :
                          'bg-green-500'
                        }`}
                        style={{ width: subscriptionData.plan === 'free' ? '25%' :
                                subscriptionData.plan === 'basic' ? '50%' :
                                subscriptionData.plan === 'premium' ? '75%' : '100%' }}
                      ></div>
                    </div>
                  </div>

                  <nav className="space-y-1">
                    {tabs.map(tab => (
                      <button
                        key={tab.id}
                        onClick={() => setActiveTab(tab.id)}
                        className={`flex items-center w-full px-3 py-2.5 text-left rounded-lg transition-colors ${
    activeTab === tab.id
                            ? 'bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400 font-medium'
                            : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600/30'
                        }`}
                      >
                        <span className="mr-3">{tab.icon}</span>
                        {tab.label}
                      </button>
                    ))}

                    <button
                      onClick={logout}
                      className="flex items-center w-full px-3 py-2.5 text-left rounded-lg text-red-600 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/20 transition-colors mt-8"
                    >
                      <FiLogOut className="mr-3" />
                      Sign Out
                    </button>
                  </nav>
                </div>

                {/* Main content */}
                <div className="flex-1 p-6 md:p-8">
                  {/* Mobile user info - only visible on mobile */}
                  <div className="md:hidden flex items-center justify-between mb-6 pb-6 border-b border-gray-200 dark:border-gray-700">
                    <div className="flex items-center">
                      <div className="relative mr-4">
                        {userData.profileImage ? (
                          <img
                            src={userData.profileImage}
                            alt={userData.name}
                            className="w-16 h-16 rounded-full object-cover border-2 border-white dark:border-gray-800 shadow-sm"
                          />
                        ) : (
                          <div className="w-16 h-16 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-400 text-xl font-bold border-2 border-white dark:border-gray-800 shadow-sm">
                            {userData.name.charAt(0).toUpperCase()}
                          </div>
                        )}

                        <button
                          onClick={() => fileInputRef.current.click()}
                          className="absolute bottom-0 right-0 bg-blue-600 hover:bg-blue-700 text-white p-1 rounded-full shadow-lg transition-colors"
                          title="Change profile picture"
                        >
                          <FiCamera className="h-3 w-3" />
                        </button>
                      </div>

                      <div>
                        <h2 className="text-lg font-bold text-gray-900 dark:text-white">{userData.name}</h2>
                        <p className="text-sm text-gray-500 dark:text-gray-400">{userData.email}</p>
                        {userData.jobTitle && (
                          <div className="mt-1 inline-block px-2 py-0.5 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-xs rounded-full">
                            {userData.jobTitle}
                          </div>
                        )}
                      </div>
                    </div>

                    <button
                      onClick={logout}
                      className="flex items-center px-3 py-1.5 text-sm rounded-lg text-red-600 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/20 transition-colors"
                    >
                      <FiLogOut className="mr-1.5" />
                      Sign Out
                    </button>
                  </div>
                  {/* Profile Tab */}
                  {activeTab === 'profile' && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.3 }}
                    >
                      <div className="flex justify-between items-center mb-6">
                        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Profile Information</h1>
                        <button
                          onClick={() => setIsEditing(!isEditing)}
                          className="flex items-center px-3 py-1.5 rounded-md bg-blue-50 text-blue-600 hover:bg-blue-100 dark:bg-blue-900/30 dark:text-blue-400 dark:hover:bg-blue-900/50 transition-colors"
                        >
                          <FiEdit2 className="mr-1.5" />
                          {isEditing ? 'Cancel' : 'Edit Profile'}
                        </button>
                      </div>

                      {isEditing ? (
                        <form onSubmit={handleProfileUpdate} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 mb-6">
                            <div>
                              <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Full Name <span className="text-red-500">*</span>
                              </label>
                              <div className="relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                  <FiUser className="h-5 w-5 text-gray-400" />
                                </div>
                                <input
                                  type="text"
                                  id="name"
                                  name="name"
                                  value={userData.name}
                                  onChange={handleInputChange}
                                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                  required
                                />
                              </div>
                            </div>

                            <div>
                              <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Email Address <span className="text-red-500">*</span>
                              </label>
                              <div className="relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                  <FiMail className="h-5 w-5 text-gray-400" />
                                </div>
                                <input
                                  type="email"
                                  id="email"
                                  name="email"
                                  value={userData.email}
                                  onChange={handleInputChange}
                                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                  required
                                />
                              </div>
                            </div>

                            <div>
                              <label htmlFor="phone" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Phone Number
                              </label>
                              <div className="relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                  </svg>
                                </div>
                                <input
                                  type="tel"
                                  id="phone"
                                  name="phone"
                                  value={userData.phone}
                                  onChange={handleInputChange}
                                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                />
                              </div>
                            </div>

                            <div>
                              <label htmlFor="jobTitle" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Job Title
                              </label>
                              <div className="relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                  </svg>
                                </div>
                                <input
                                  type="text"
                                  id="jobTitle"
                                  name="jobTitle"
                                  value={userData.jobTitle}
                                  onChange={handleInputChange}
                                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                />
                              </div>
                            </div>

                            <div>
                              <label htmlFor="company" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Company
                              </label>
                              <div className="relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                  </svg>
                                </div>
                                <input
                                  type="text"
                                  id="company"
                                  name="company"
                                  value={userData.company}
                                  onChange={handleInputChange}
                                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                />
                              </div>
                            </div>

                            <div>
                              <label htmlFor="website" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Website
                              </label>
                              <div className="relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                  <FiGlobe className="h-5 w-5 text-gray-400" />
                                </div>
                                <input
                                  type="url"
                                  id="website"
                                  name="website"
                                  value={userData.website}
                                  onChange={handleInputChange}
                                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                  placeholder="https://example.com"
                                />
                              </div>
                            </div>

                            <div>
                              <label htmlFor="location" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Location
                              </label>
                              <div className="relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                  <FiMapPin className="h-5 w-5 text-gray-400" />
                                </div>
                                <input
                                  type="text"
                                  id="location"
                                  name="location"
                                  value={userData.location}
                                  onChange={handleInputChange}
                                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                />
                              </div>
                            </div>
                          </div>

                          <div className="mb-6">
                            <label htmlFor="bio" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                              Bio
                            </label>
                            <textarea
                              id="bio"
                              name="bio"
                              rows="4"
                              value={userData.bio}
                              onChange={handleInputChange}
                              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                              placeholder="Tell us about yourself..."
                            ></textarea>
                            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                              Brief description for your profile. URLs are hyperlinked.
                            </p>
                          </div>

                          <div className="flex justify-end space-x-3">
                            <button
                              type="button"
                              onClick={() => setIsEditing(false)}
                              className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600 transition-colors"
                            >
                              Cancel
                            </button>
                            <button
                              type="submit"
                              disabled={isSubmitting}
                              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-blue-700 dark:hover:bg-blue-800 transition-colors"
                            >
                              {isSubmitting ? (
                                <>
                                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                  </svg>
                                  Saving...
                                </>
                              ) : (
                                'Save Changes'
                              )}
                            </button>
                          </div>
                        </form>
                      ) : (
                        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                            <div>
                              <div className="flex items-center">
                                <FiUser className="h-5 w-5 text-gray-400 mr-2" />
                                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Full Name</h3>
                              </div>
                              <p className="mt-1 text-gray-900 dark:text-white font-medium">{userData.name}</p>
                            </div>

                            <div>
                              <div className="flex items-center">
                                <FiMail className="h-5 w-5 text-gray-400 mr-2" />
                                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Email Address</h3>
                              </div>
                              <p className="mt-1 text-gray-900 dark:text-white">{userData.email}</p>
                            </div>

                            <div>
                              <div className="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                </svg>
                                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Phone Number</h3>
                              </div>
                              <p className="mt-1 text-gray-900 dark:text-white">{userData.phone || '-'}</p>
                            </div>

                            <div>
                              <div className="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                </svg>
                                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Job Title</h3>
                              </div>
                              <p className="mt-1 text-gray-900 dark:text-white">{userData.jobTitle || '-'}</p>
                            </div>

                            <div>
                              <div className="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                </svg>
                                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Company</h3>
                              </div>
                              <p className="mt-1 text-gray-900 dark:text-white">{userData.company || '-'}</p>
                            </div>

                            <div>
                              <div className="flex items-center">
                                <FiGlobe className="h-5 w-5 text-gray-400 mr-2" />
                                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Website</h3>
                              </div>
                              <p className="mt-1 text-gray-900 dark:text-white">
                                {userData.website ? (
                                  <a href={userData.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline dark:text-blue-400">
                                    {userData.website}
                                  </a>
                                ) : (
                                  '-'
                                )}
                              </p>
                            </div>

                            <div>
                              <div className="flex items-center">
                                <FiMapPin className="h-5 w-5 text-gray-400 mr-2" />
                                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Location</h3>
                              </div>
                              <p className="mt-1 text-gray-900 dark:text-white">{userData.location || '-'}</p>
                            </div>
                          </div>

                          <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                            <div className="flex items-center">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                              </svg>
                              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Bio</h3>
                            </div>
                            <p className="mt-1 text-gray-900 dark:text-white whitespace-pre-line">{userData.bio || '-'}</p>
                          </div>
                        </div>
                      )}
                    </motion.div>
                  )}

                  {/* Saved Models Tab */}
                  {activeTab === 'saved' && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.3 }}
                    >
                      <div className="flex justify-between items-center mb-6">
                        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Saved Models</h1>
                      </div>

                      {savedModels.length > 0 ? (
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                          {savedModels.map(model => (
                            <div key={model.id} className="bg-white dark:bg-gray-700 rounded-lg shadow overflow-hidden">
                              <img src={model.imageUrl} alt={model.title} className="w-full h-32 object-cover" />
                              <div className="p-4">
                                <h3 className="font-medium text-gray-900 dark:text-white">{model.title}</h3>
                                <p className="text-sm text-gray-500 dark:text-gray-400">{model.category}</p>
                                <div className="mt-4 flex justify-between items-center">
                                  <Link to={`/model/${model.id}`} className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm">
                                    View Details
                                  </Link>
                                  <span className="text-xs text-gray-500 dark:text-gray-400">Saved on {model.date}</span>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-12">
                          <FiHeart className="mx-auto h-12 w-12 text-gray-400" />
                          <h3 className="mt-2 text-lg font-medium text-gray-900 dark:text-white">No saved models</h3>
                          <p className="mt-1 text-gray-500 dark:text-gray-400">You haven't saved any models yet.</p>
                          <div className="mt-6">
                            <Link to="/" className="btn btn-primary">
                              Browse Models
                            </Link>
                          </div>
                        </div>
                      )}
                    </motion.div>
                  )}

                  {/* Activity Tab */}
                  {activeTab === 'activity' && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.3 }}
                    >
                      <div className="flex justify-between items-center mb-6">
                        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Activity History</h1>
                      </div>

                      {activityHistory.length > 0 ? (
                        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
                          <ul className="divide-y divide-gray-200 dark:divide-gray-700">
                            {activityHistory.map((activity) => {
                              // Format date
                              const date = new Date(activity.date);
                              const formattedDate = new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
                                month: 'short',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                              }).format(date);

                              // Determine icon based on activity type
                              let icon;
                              let bgColor;

                              switch (activity.type) {
      case 'download':
                                  icon = <FiDownload className="h-5 w-5" />;
                                  bgColor = 'bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400';
                                  break;
                                case 'upload':
                                  icon = <FiUpload className="h-5 w-5" />;
                                  bgColor = 'bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400';
                                  break;
                                case 'save':
                                  icon = <FiHeart className="h-5 w-5" />;
                                  bgColor = 'bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400';
                                  break;
                                case 'comment':
                                  icon = <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                                  </svg>;
                                  bgColor = 'bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400';
                                  break;
                                default:
                                  icon = <FiActivity className="h-5 w-5" />;
                                  bgColor = 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400';
                              }

                              return (
                                <li key={activity.id} className="p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                                  <div className="flex items-start space-x-4">
                                    <div className={`flex-shrink-0 rounded-full p-2 ${bgColor}`}>
                                      {icon}
                                    </div>
                                    <div className="flex-1 min-w-0">
                                      <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                                        {activity.type === 'download' && 'Downloaded'}
                                        {activity.type === 'upload' && 'Uploaded'}
                                        {activity.type === 'save' && 'Saved'}
                                        {activity.type === 'comment' && 'Commented on'}
                                        {' '}
                                        <Link to={`/model/${activity.id}`} className="text-blue-600 hover:underline dark:text-blue-400">
                                          {activity.title}
                                        </Link>
                                      </p>
                                      <div className="mt-1 flex items-center">
                                        <FiCalendar className="flex-shrink-0 mr-1.5 h-4 w-4 text-gray-400" />
                                        <p className="text-sm text-gray-500 dark:text-gray-400 truncate">{formattedDate}</p>
                                      </div>
                                    </div>
                                    <div className="flex-shrink-0">
                                      <Link to={`/model/${activity.id}`} className="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-400 dark:hover:bg-blue-900/50 transition-colors">
                                        View
                                      </Link>
                                    </div>
                                  </div>
                                </li>
                              );
                            })}
                          </ul>
                        </div>
                      ) : (
                        <div className="text-center py-12 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                          <FiActivity className="mx-auto h-12 w-12 text-gray-400" />
                          <h3 className="mt-2 text-lg font-medium text-gray-900 dark:text-white">No activity yet</h3>
                          <p className="mt-1 text-gray-500 dark:text-gray-400">Your recent activity will appear here.</p>
                          <div className="mt-6">
                            <Link to="/" className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-700 dark:hover:bg-blue-800 transition-colors">
                              Browse Models
                            </Link>
                          </div>
                        </div>
                      )}
                    </motion.div>
                  )}

                  {/* Downloads Tab */}
                  {activeTab === 'downloads' && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.3 }}
                    >
                      <div className="flex justify-between items-center mb-6">
                        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Download History</h1>
                      </div>

                      {downloadHistory.length > 0 ? (
                        <div className="overflow-x-auto">
                          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead className="bg-gray-50 dark:bg-gray-800">
                              <tr>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                  Model
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                  Category
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                  Date
                                </th>
                                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                  Actions
                                </th>
                              </tr>
                            </thead>
                            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                              {downloadHistory.map(model => (
                                <tr key={model.id}>
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="flex items-center">
                                      <div className="flex-shrink-0 h-10 w-10">
                                        <img className="h-10 w-10 rounded-md object-cover" src={model.imageUrl} alt={model.title} />
                                      </div>
                                      <div className="ml-4">
                                        <div className="text-sm font-medium text-gray-900 dark:text-white">{model.title}</div>
                                      </div>
                                    </div>
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="text-sm text-gray-500 dark:text-gray-400">{model.category}</div>
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="text-sm text-gray-500 dark:text-gray-400">{model.date}</div>
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <Link to={`/model/${model.id}`} className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mr-4">
                                      View
                                    </Link>
                                    <button className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                      Download Again
                                    </button>
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      ) : (
                        <div className="text-center py-12">
                          <FiDownload className="mx-auto h-12 w-12 text-gray-400" />
                          <h3 className="mt-2 text-lg font-medium text-gray-900 dark:text-white">No downloads yet</h3>
                          <p className="mt-1 text-gray-500 dark:text-gray-400">You haven't downloaded any models yet.</p>
                          <div className="mt-6">
                            <Link to="/" className="btn btn-primary">
                              Browse Models
                            </Link>
                          </div>
                        </div>
                      )}
                    </motion.div>
                  )}

                  {/* Security Tab */}
                  {activeTab === 'security' && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.3 }}
                    >
                      <div className="mb-6">
                        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Security Settings</h1>
                      </div>

                      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
                        <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
                          <FiLock className="mr-2 h-5 w-5 text-gray-500" />
                          Change Password
                        </h2>
                        <form onSubmit={handlePasswordUpdate}>
                          <div className="space-y-4">
                            <div>
                              <label htmlFor="currentPassword" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Current Password
                              </label>
                              <div className="relative">
                                <input
                                  type={showPassword ? "text" : "password"}
                                  id="currentPassword"
                                  name="currentPassword"
                                  value={passwordData.currentPassword}
                                  onChange={handlePasswordChange}
                                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                  required
                                />
                                <button
                                  type="button"
                                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                                  onClick={() => setShowPassword(!showPassword)}
                                >
                                  {showPassword ? (
                                    <FiEyeOff className="h-5 w-5" />
                                  ) : (
                                    <FiEye className="h-5 w-5" />
                                  )}
                                </button>
                              </div>
                            </div>

                            <div>
                              <label htmlFor="newPassword" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                New Password
                              </label>
                              <div className="relative">
                                <input
                                  type={showNewPassword ? "text" : "password"}
                                  id="newPassword"
                                  name="newPassword"
                                  value={passwordData.newPassword}
                                  onChange={handlePasswordChange}
                                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                  required
                                />
                                <button
                                  type="button"
                                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                                  onClick={() => setShowNewPassword(!showNewPassword)}
                                >
                                  {showNewPassword ? (
                                    <FiEyeOff className="h-5 w-5" />
                                  ) : (
                                    <FiEye className="h-5 w-5" />
                                  )}
                                </button>
                              </div>

                              {/* Password strength meter */}
                              {passwordData.newPassword && (
                                <div className="mt-2">
                                  <div className="flex items-center justify-between mb-1">
                                    <div className="text-xs text-gray-600 dark:text-gray-400">Password strength:</div>
                                    <div className={`text-xs font-medium ${
                                      passwordStrength <= 2 ? 'text-red-500' :
                                      passwordStrength <= 4 ? 'text-yellow-500' :
                                      'text-green-500'
                                    }`}>
                                      {getStrengthLabel()}
                                    </div>
                                  </div>
                                  <div className="h-1.5 w-full bg-gray-200 rounded-full overflow-hidden dark:bg-gray-700">
                                    <div
                                      className={`h-full ${getStrengthColor()}`}
                                      style={{ width: `${(passwordStrength / 5) * 100}%` }}
                                    ></div>
                                  </div>
                                </div>
                              )}
                            </div>

                            <div>
                              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Confirm New Password
                              </label>
                              <div className="relative">
                                <input
                                  type={showConfirmPassword ? "text" : "password"}
                                  id="confirmPassword"
                                  name="confirmPassword"
                                  value={passwordData.confirmPassword}
                                  onChange={handlePasswordChange}
                                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                  required
                                />
                                <button
                                  type="button"
                                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                >
                                  {showConfirmPassword ? (
                                    <FiEyeOff className="h-5 w-5" />
                                  ) : (
                                    <FiEye className="h-5 w-5" />
                                  )}
                                </button>
                              </div>

                              {/* Password match indicator */}
                              {passwordData.confirmPassword && (
                                <div className="mt-1 flex items-center">
                                  {passwordData.newPassword === passwordData.confirmPassword ? (
                                    <>
                                      <FiCheckCircle className="h-4 w-4 text-green-500 mr-1" />
                                      <span className="text-xs text-green-500">Passwords match</span>
                                    </>
                                  ) : (
                                    <>
                                      <FiAlertCircle className="h-4 w-4 text-red-500 mr-1" />
                                      <span className="text-xs text-red-500">Passwords do not match</span>
                                    </>
                                  )}
                                </div>
                              )}
                            </div>
                          </div>

                          <div className="mt-6">
                            <button
                              type="submit"
                              disabled={isSubmitting}
                              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-blue-700 dark:hover:bg-blue-800 transition-colors"
                            >
                              {isSubmitting ? (
                                <>
                                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                  </svg>
                                  Updating...
                                </>
                              ) : (
                                'Update Password'
                              )}
                            </button>
                          </div>
                        </form>
                      </div>

                      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                        <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
                          <FiShield className="mr-2 h-5 w-5 text-gray-500" />
                          Two-Factor Authentication
                        </h2>

                        <div className="flex items-center justify-between mb-4">
                          <div>
                            <p className="text-gray-700 dark:text-gray-300">
                              Add an extra layer of security to your account by enabling two-factor authentication.
                            </p>
                            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                              We'll ask for a verification code in addition to your password when you sign in.
                            </p>
                          </div>
                          <div className="ml-4">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              is2FAEnabled
                                ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                                : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                            }`}>
                              {is2FAEnabled ? 'Enabled' : 'Disabled'}
                            </span>
                          </div>
                        </div>

                        {showQRCode ? (
                          <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 mb-4">
                            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                              Scan this QR code with your authenticator app
                            </h3>
                            <div className="flex justify-center mb-4">
                              <div className="bg-white p-2 rounded-lg">
                                <img
                                  src="https://upload.wikimedia.org/wikipedia/commons/thumb/d/d0/QR_code_for_mobile_English_Wikipedia.svg/1200px-QR_code_for_mobile_English_Wikipedia.svg.png"
                                  alt="2FA QR Code"
                                  className="w-48 h-48"
                                />
                              </div>
                            </div>
                            <div className="mb-4">
                              <label htmlFor="verificationCode" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Enter the 6-digit verification code
                              </label>
                              <div className="flex space-x-2">
                                <input
                                  type="text"
                                  id="verificationCode"
                                  value={verificationCode}
                                  onChange={(e) => setVerificationCode(e.target.value.replace(/[^0-9]/g, ').slice(0, 6))}
                                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                  placeholder="000000"
                                  maxLength={6}
                                  required
                                />
                                <button
                                  type="button"
                                  onClick={handleVerify2FA}
                                  disabled={isSubmitting || verificationCode.length !== 6}
                                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-blue-700 dark:hover:bg-blue-800 transition-colors"
                                >
                                  {isSubmitting ? (
                                    <>
                                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                      </svg>
                                      Verifying...
                                    </>
                                  ) : (
                                    'Verify'
                                  )}
                                </button>
                              </div>
                            </div>
                            <div className="flex justify-end">
                              <button
                                type="button"
                                onClick={() => setShowQRCode(false)}
                                className="text-sm text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200"
                              >
                                Cancel
                              </button>
                            </div>
                          </div>
                        ) : (
                          <button
                            type="button"
                            onClick={handle2FAToggle}
                            disabled={isSubmitting}
                            className={`inline-flex items-center px-4 py-2 border rounded-md shadow-sm text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors ${
                              is2FAEnabled
                                ? 'border-red-300 text-red-700 bg-red-50 hover:bg-red-100 dark:bg-red-900/10 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/20'
                                : 'border-transparent text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800'
                            }`}
                          >
                            {isSubmitting ? (
                              <>
                                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-current" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                {is2FAEnabled ? 'Disabling...' : 'Enabling...'}
                              </>
                            ) : (
                              is2FAEnabled ? 'Disable 2FA' : 'Enable 2FA'
                            )}
                          </button>
                        )}
                      </div>
                    </motion.div>
                  )}

                  {/* Billing Tab */}
                  {activeTab === 'billing' && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.3 }}
                    >
                      <div className="flex justify-between items-center mb-6">
                        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Billing & Subscription</h1>
                        <button
                          type="button"
                          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-700 dark:hover:bg-blue-800 transition-colors"
                        >
                          Upgrade Plan
                        </button>
                      </div>

                      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
                        <div className="flex justify-between items-start">
                          <div>
                            <h2 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
                              <FiCreditCard className="mr-2 h-5 w-5 text-gray-500" />
                              Current Plan
                            </h2>
                            <div className="mt-1 flex items-center">
                              <span className="text-2xl font-bold text-gray-900 dark:text-white capitalize">
                                {subscriptionData.plan}
                              </span>
                              <span className={`ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                subscriptionData.status === 'active'
                                  ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                                  : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
                              }`}>
                                {subscriptionData.status === 'active' ? 'Active' : 'Pending'}
                              </span>
                            </div>
                          </div>

                          {subscriptionData.plan !== 'free' && subscriptionData.nextBillingDate && (
                            <div className="text-right">
                              <span className="text-sm text-gray-500 dark:text-gray-400">Next billing date</span>
                              <p className="text-gray-900 dark:text-white">
                                {new Date(subscriptionData.nextBillingDate).toLocaleDateString('en-US', {
    year: 'numeric',
                                  month: 'long',
                                  day: 'numeric'
                                })}
                              </p>
                            </div>
                          )}
                        </div>

                        <div className="mt-6">
                          <div className="flex items-center justify-between mb-2">
                            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">Downloads</h3>
                            <span className="text-sm text-gray-500 dark:text-gray-400">
                              {currentUser?.downloadCredits || 0} remaining this month
                            </span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                            <div
                              className="bg-blue-600 h-2.5 rounded-full dark:bg-blue-500"
                              style={{ width: `${Math.min(100, ((currentUser?.downloadCredits || 0) / (subscriptionData.plan === 'free' ? 5 : subscriptionData.plan === 'basic' ? 20 : 100)) * 100)}%` }}
                            ></div>
                          </div>
                        </div>

                        <div className="mt-6">
                          <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Plan includes:</h3>
                          <ul className="space-y-3 text-sm text-gray-700 dark:text-gray-300">
                            {subscriptionData.plan === 'free' && (
                              <>
                                <li className="flex items-start">
                                  <svg className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                                  </svg>
                                  <span>5 downloads per month</span>
                                </li>
                                <li className="flex items-start">
                                  <svg className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                                  </svg>
                                  <span>Access to free models only</span>
                                </li>
                                <li className="flex items-start">
                                  <svg className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                                  </svg>
                                  <span>Community support</span>
                                </li>
                              </>
                            )}

                            {subscriptionData.plan === 'basic' && (
                              <>
                                <li className="flex items-start">
                                  <svg className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                                  </svg>
                                  <span>20 downloads per month</span>
                                </li>
                                <li className="flex items-start">
                                  <svg className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                                  </svg>
                                  <span>Access to premium models</span>
                                </li>
                                <li className="flex items-start">
                                  <svg className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                                  </svg>
                                  <span>Email support</span>
                                </li>
                              </>
                            )}

                            {subscriptionData.plan === 'premium' && (
                              <>
                                <li className="flex items-start">
                                  <svg className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                                  </svg>
                                  <span>Unlimited downloads</span>
                                </li>
                                <li className="flex items-start">
                                  <svg className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                                  </svg>
                                  <span>Access to all models including exclusive content</span>
                                </li>
                                <li className="flex items-start">
                                  <svg className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                                  </svg>
                                  <span>Priority support</span>
                                </li>
                                <li className="flex items-start">
                                  <svg className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                                  </svg>
                                  <span>Early access to new features</span>
                                </li>
                              </>
                            )}
                          </ul>
                        </div>

                        {subscriptionData.plan !== 'premium' && (
                          <div className="mt-6">
                            <button
                              type="button"
                              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-700 dark:hover:bg-blue-800 transition-colors"
                            >
                              Upgrade to {subscriptionData.plan === 'free' ? 'Basic' : 'Premium'}
                            </button>
                          </div>
                        )}
                      </div>

                      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                        <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                          </svg>
                          Payment Methods
                        </h2>

                        {subscriptionData.paymentMethod ? (
                          <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 flex items-center justify-between">
                            <div className="flex items-center">
                              <div className="flex-shrink-0">
                                {subscriptionData.paymentMethod.brand === 'Visa' && (
                                  <svg className="h-8 w-8" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <rect width="48" height="48" rx="6" fill="#2566AF"/>
                                    <path d="M18.4 30.9H14.4L12 19.1H16L18.4 30.9Z" fill="white"/>
                                    <path d="M33.2 19.4C32.3 19.1 30.9 18.8 29.2 18.8C25.5 18.8 22.8 20.7 22.8 23.4C22.8 25.5 24.8 26.6 26.3 27.3C27.8 28 28.3 28.5 28.3 29.1C28.3 30 27.2 30.4 26.1 30.4C24.6 30.4 23.8 30.2 22.4 29.6L21.8 29.3L21.2 32.7C22.3 33.1 24.1 33.5 26 33.5C29.9 33.5 32.6 31.6 32.6 28.7C32.6 27 31.5 25.7 29.2 24.7C27.8 24.1 26.9 23.6 26.9 22.9C26.9 22.3 27.6 21.7 29.1 21.7C30.3 21.7 31.2 21.9 31.9 22.2L32.3 22.4L32.9 19.1L33.2 19.4Z" fill="white"/>
                                    <path d="M37.6 19.1H34.6C33.8 19.1 33.2 19.3 32.9 20L28.8 30.9H32.7L33.4 29.1H37.7L38.1 30.9H41.6L38.9 19.1H37.6ZM34.4 26.4C34.7 25.5 35.8 22.7 35.8 22.7C35.8 22.7 36.1 21.9 36.3 21.4L36.5 22.6C36.5 22.6 37.2 25.5 37.3 26.4H34.4Z" fill="white"/>
                                    <path d="M24.4 19.1L20.8 26.9L20.4 25.1C19.7 23 17.7 20.8 15.5 19.7L18.9 30.9H22.8L28.3 19.1H24.4Z" fill="white"/>
                                  </svg>
                                )}
                                {subscriptionData.paymentMethod.brand === 'Mastercard' && (
                                  <svg className="h-8 w-8" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <rect width="48" height="48" rx="6" fill="#F7F7F7"/>
                                    <path d="M29.2 16H18.8V32H29.2V16Z" fill="#FF5F00"/>
                                    <path d="M19.6 24C19.6 20.8 21.2 18 23.6 16.4C22.4 15.6 20.8 15.2 19.2 15.2C14.8 15.2 11.2 19.2 11.2 24C11.2 28.8 14.8 32.8 19.2 32.8C20.8 32.8 22.4 32.4 23.6 31.6C21.2 30 19.6 27.2 19.6 24Z" fill="#EB001B"/>
                                    <path d="M36.8 24C36.8 28.8 33.2 32.8 28.8 32.8C27.2 32.8 25.6 32.4 24.4 31.6C26.8 30 28.4 27.2 28.4 24C28.4 20.8 26.8 18 24.4 16.4C25.6 15.6 27.2 15.2 28.8 15.2C33.2 15.2 36.8 19.2 36.8 24Z" fill="#F79E1B"/>
                                  </svg>
                                )}
                              </div>
                              <div className="ml-4">
                                <p className="text-sm font-medium text-gray-900 dark:text-white">
                                  {subscriptionData.paymentMethod.brand} •••• {subscriptionData.paymentMethod.last4}
                                </p>
                                <p className="text-sm text-gray-500 dark:text-gray-400">
                                  Expires {subscriptionData.paymentMethod.expiryMonth}/{subscriptionData.paymentMethod.expiryYear}
                                </p>
                              </div>
                            </div>
                            <div>
                              <button
                                type="button"
                                className="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-sm font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600 transition-colors"
                              >
                                Edit
                              </button>
                            </div>
                          </div>
                        ) : (
                          <div className="text-center py-6">
                            <svg xmlns="http://www.w3.org/2000/svg" className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                            </svg>
                            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No payment methods</h3>
                            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                              Add a payment method to upgrade your plan.
                            </p>
                            <div className="mt-6">
                              <button
                                type="button"
                                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-700 dark:hover:bg-blue-800 transition-colors"
                              >
                                Add Payment Method
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    </motion.div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
        </main>
      </PageTransition>

    </div>
  );
};

export default Profile;
