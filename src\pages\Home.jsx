import React, { useState, useEffect, useCallback } from 'react';
import FilterSection from '../components/FilterSection';
import ModelGallery from '../components/ModelGallery';
import Pagination from '../components/Pagination';
import LoadingIndicator from '../components/ui/LoadingIndicator';
import Toast from '../components/ui/Toast';
import Tooltip from '../components/ui/Tooltip';
import { motion } from 'framer-motion';
import { FiFilter, FiGrid, FiList, FiRefreshCw } from 'react-icons/fi';
import { useModels } from '../context/ModelContext';
import apiService from '../services/api';
import realDataService from '../services/realDataService';

const Home = () => {
  const { models: contextModels } = useModels();
  const [models, setModels] = useState([]);
  const [filteredModels, setFilteredModels] = useState([]);
  const [filters, setFilters] = useState({
    format: 'All',
    scene: 'All',
    model: 'All',
    color: 'All',
    style: 'All',
    renderEngine: 'All',
    searchTerm: ''
  });
  const [currentPage, setCurrentPage] = useState(0);
  const [modelsPerPage] = useState(8);
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState('grid');  // 'grid' or 'list'
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastType, setToastType] = useState('info');
  // Fetch models using realDataService
  useEffect(() => {
    const fetchModels = async () => {
  setLoading(true);
      try {
        // Get all models from realDataService
        const allModels = await realDataService.getAllModels();

        if (cachedData && !isExpired(cachedData)) {
  setModels(allModels);
          setFilteredModels(allModels);
        } else {
          setModels([]);
          setFilteredModels([]);
          setShowToast(true);
          setToastMessage('No models available at the moment.');
          setToastType('info');
        }
      } catch (error) {
        setShowToast(true);
        setToastMessage('Failed to load models. Please try again later.';
        setToastType('error';
      } finally {
        setLoading(false);
      }
    };

    fetchModels();
  }, []);

  // Apply filters
  useEffect(() => {
  if (models.length === 0) return;

    let result = [...models];

    // Apply search filter
    if (cachedData && !isExpired(cachedData)) {
  const searchLower = filters.searchTerm.toLowerCase();
      result = result.filter(model =>
        model.title.toLowerCase().includes(searchLower) ||
        model.description?.toLowerCase().includes(searchLower) ||
        model.category.toLowerCase().includes(searchLower) ||
        model.subcategory?.toLowerCase().includes(searchLower)
      );
    }

    // Apply category filter
    if (cachedData && !isExpired(cachedData)) {
  result = result.filter(model =>
        model.category === filters.model || model.subcategory === filters.model
      );
    }

    // Apply format filter
    if (cachedData && !isExpired(cachedData)) {
  result = result.filter(model =>
        model.format.includes(filters.format)
      );
    }

    // Apply scene filter
    if (cachedData && !isExpired(cachedData)) {
  result = result.filter(model =>
        (model.category === 'Residential' || model.category === 'Commercial' ||
         model.category === 'Exterior') &&
        (model.subcategory?.includes(filters.scene) || model.category.includes(filters.scene))
      );
    }

    // Apply style filter (this would be more detailed in a real app)
    if (filters.style !== 'All') {
      // For demo purposes, we'll just filter by description containing the style
      result = result.filter(model =>
        model.description?.toLowerCase().includes(filters.style.toLowerCase())
      );
    }

    setFilteredModels(result);
    setCurrentPage(0); // Reset to first page when filters change
  }, [filters, models]);

  // Get current models for pagination
  const indexOfLastModel = (currentPage + 1) * modelsPerPage;
  const indexOfFirstModel = indexOfLastModel - modelsPerPage;
  const currentModels = filteredModels.slice(indexOfFirstModel, indexOfLastModel);
  const pageCount = Math.ceil(filteredModels.length / modelsPerPage);

  const handleFilterChange = (newFilters) => {
  setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const handlePageChange = ({ selected }) => {
  setCurrentPage(selected);
    // Scroll to top when changing page
    window.scrollTo(0, 0);
  };

  // Show toast message
  const showToastMessage = (message, type = 'info') => {
  setToastMessage(message);
    setToastType(type);
    setShowToast(true);

    // Auto-hide toast after 5 seconds
    setTimeout(() => {
  setShowToast(false);
    }, 5000);
  };

  // Toggle view mode
  const toggleViewMode = () => {
    const newMode = viewMode === 'grid' ? 'list' : 'grid';
    setViewMode(newMode);
    showToastMessage(`Switched to ${newMode} view`);
  };

  // Reset filters
  const resetFilters = () => {
    setFilters({
    format: 'All',
      scene: 'All',
      model: 'All',
      color: 'All',
      style: 'All',
      renderEngine: 'All',
      searchTerm: '
    });
    showToastMessage('Filters have been reset', 'success';
  };

  return (
    <div className="flex flex-col min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Hero Section */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
        className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-16"
      >
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            Premium 3D Models for Your Projects
          </h1>
          <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
            Discover high-quality 3D models, scenes, and assets for architectural visualization and design
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <button className="bg-white text-blue-700 hover:bg-gray-100 px-6 py-3 rounded-lg font-medium text-lg transition-colors">
              Browse Models
            </button>
            <button className="bg-transparent border-2 border-white hover:bg-white/10 px-6 py-3 rounded-lg font-medium text-lg transition-colors">
              Learn More
            </button>
          </div>
        </div>
      </motion.div>

      <div id="filter-section">
        <FilterSection onFilterChange={handleFilterChange} />
      </div>

      <main className="flex-grow container mx-auto px-4 py-8">
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <LoadingIndicator type="spinner" size="lg" text="Loading models..." />
          </div>
        ) : (
          <>
            <div className="flex flex-col sm:flex-row justify-between items-center mb-6 gap-4">
              <div className="flex items-center">
                <h2 className="text-xl font-semibold text-gray-800 dark:text-white mr-4">
                  {filteredModels.length} models found
                </h2>
                <div className="flex space-x-2">
                  <Tooltip content="Reset filters">
                    <button
                      onClick={resetFilters}
                      className="p-2 text-gray-600 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400 bg-white dark:bg-gray-800 rounded-md shadow-sm border border-gray-200 dark:border-gray-700 transition-colors"
                    >
                      <FiRefreshCw className="w-5 h-5" />
                    </button>
                  </Tooltip>

                  <Tooltip content="Toggle view mode">
                    <button
                      onClick={toggleViewMode}
                      className="p-2 text-gray-600 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400 bg-white dark:bg-gray-800 rounded-md shadow-sm border border-gray-200 dark:border-gray-700 transition-colors"
                    >
                      {viewMode === 'grid' ? <FiList className="w-5 h-5" /> : <FiGrid className="w-5 h-5" />}
                    </button>
                  </Tooltip>

                  <Tooltip content="Filter options">
                    <button
                      onClick={() => document.getElementById('filter-section').scrollIntoView({ behavior: 'smooth' })}
                      className="p-2 text-gray-600 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400 bg-white dark:bg-gray-800 rounded-md shadow-sm border border-gray-200 dark:border-gray-700 transition-colors"
                    >
                      <FiFilter className="w-5 h-5" />
                    </button>
                  </Tooltip>
                </div>
              </div>

              <div className="flex items-center">
                <label htmlFor="sort" className="mr-2 text-gray-700 dark:text-gray-300">Sort by:</label>
                <select
                  id="sort"
                  className="border border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 dark:bg-gray-700 dark:border-gray-600 dark:text-white p-2"
                >
                  <option value="newest">Newest</option>
                  <option value="popular">Most Popular</option>
                  <option value="name">Name (A-Z)</option>
                  <option value="price-low">Price: Low to High</option>
                  <option value="price-high">Price: High to Low</option>
                  <option value="rating">Highest Rated</option>
                </select>
              </div>
            </div>

            <ModelGallery models={currentModels} viewMode={viewMode} />

            {pageCount > 1 && (
              <div className="mt-8">
                <Pagination
                  pageCount={pageCount}
                  currentPage={currentPage}
                  onPageChange={handlePageChange}
                />
              </div>
            )}
          </>
        )}
      </main>

      {/* Toast notifications */}
      {showToast && (
        <Toast
          type={toastType}
          message={toastMessage}
          visible={showToast}
          onClose={() => setShowToast(false)}
          duration={5000}
          position="bottom-right"
        />
      )}
    </div>
  );
};

export default Home;
