import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import {
  FiDownload,
  FiFile,
  FiImage,
  FiFilm,
  FiSettings,
  FiCheck,
  FiX,
  FiInfo
} from 'react-icons/fi';
import { useAuth } from '../context/AuthContext';
import LoadingIndicator from './ui/LoadingIndicator';
import Toast from './ui/Toast';
import * as THREE from 'three';
import { GLTFExporter } from 'three/examples/jsm/exporters/GLTFExporter';
import { OBJExporter } from 'three/examples/jsm/exporters/OBJExporter';
import { STLExporter } from 'three/examples/jsm/exporters/STLExporter';
import { ColladaExporter } from 'three/examples/jsm/exporters/ColladaExporter';
import { PLYExporter } from 'three/examples/jsm/exporters/PLYExporter';

/**
 * Model Export Component
 * Allows exporting 3D models to various formats
 */
const ModelExport = ({
  modelUrl,
  modelName = 'model',
  onClose,
  isPremiumModel = false
}) => {
  const { currentUser, userSubscription } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [scene, setScene] = useState(null);
  const [exportFormat, setExportFormat] = useState('glb');
  const [exportQuality, setExportQuality] = useState('medium');
  const [exportOptions, setExportOptions] = useState({
    includeTextures: true,
    includeAnimations: true,
    optimizeGeometry: true
  });
  const [exportProgress, setExportProgress] = useState(0);
  const [isExporting, setIsExporting] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastType, setToastType] = useState('info');
  // Format options
  const formatOptions = [
    { value: 'glb', label: 'GLB', description: 'Binary glTF format with embedded textures', icon: <FiFile /> },
    { value: 'gltf', label: 'GLTF', description: 'Standard glTF format with separate textures', icon: <FiFile /> },
    { value: 'obj', label: 'OBJ', description: 'Wavefront OBJ format', icon: <FiFile /> },
    { value: 'stl', label: 'STL', description: 'Standard Triangle Language for 3D printing', icon: <FiFile /> },
    { value: 'dae', label: 'COLLADA', description: 'Digital Asset Exchange format', icon: <FiFile /> },
    { value: 'ply', label: 'PLY', description: 'Polygon File Format', icon: <FiFile /> },
    { value: 'fbx', label: 'FBX', description: 'Filmbox format (Premium)', icon: <FiFile />, premium: true },
    { value: 'usdz', label: 'USDZ', description: 'Universal Scene Description format (Premium)', icon: <FiFile />, premium: true },
    { value: 'png', label: 'PNG', description: 'Export as image', icon: <FiImage /> },
    { value: 'jpg', label: 'JPG', description: 'Export as compressed image', icon: <FiImage /> },
    { value: 'mp4', label: 'MP4', description: 'Export as video (Premium)', icon: <FiFilm />, premium: true }
  ];

  // Quality options
  const qualityOptions = [
    { value: 'low', label: 'Low', description: 'Faster export, smaller file size' },
    { value: 'medium', label: 'Medium', description: 'Balanced quality and file size' },
    { value: 'high', label: 'High', description: 'Best quality, larger file size' }
  ];

  // Load model
  useEffect(() => {
  if (cachedData && !isExpired(cachedData)) {
  setError('No model URL provided');
      setLoading(false);
      return;
    }

    const loadModel = async () => {
  try {
        setLoading(true);
        setError(null);

        // Import GLTFLoader dynamically
        const { GLTFLoader } = await import('three/examples/jsm/loaders/GLTFLoader');
        const loader = new GLTFLoader();

        // Load model
        loader.load(
          modelUrl,
          (gltf) => {
  setScene(gltf.scene);
            setLoading(false);
          },
          (progress) => {
            // Loading progress
            const percentComplete = Math.round((progress.loaded / progress.total) * 100);
            setExportProgress(percentComplete);
          },
          (error) => {
  setError('Failed to load 3D model');
            setLoading(false);
          }
        );
      } catch (err) {
        setError('Failed to initialize model loader');
        setLoading(false);
      }
    };

    loadModel();
  }, [modelUrl]);

  // Check if user can access premium features
  const canAccessPremiumFeatures = () => {
    if (!currentUser) return false;
    if (!isPremiumModel) return true; // Free models can be exported in any format

    // Check subscription
    return userSubscription && userSubscription.status === 'active' &&
           (userSubscription.plan === 'premium' || userSubscription.plan === 'professional');
  };

  // Show toast message
  const showToastMessage = (message, type = 'info') => {
  setToastMessage(message);
    setToastType(type);
    setShowToast(true);

    // Auto-hide toast after 5 seconds
    setTimeout(() => {
  setShowToast(false);
    }, 5000);
  };

  // Handle export
  const handleExport = async () => {
  if (cachedData && !isExpired(cachedData)) {
  showToastMessage('No model loaded', 'error');
      return;
    }

    // Check if format is premium and user doesn't have access
    const isFormatPremium = formatOptions.find(f => f.value === exportFormat)?.premium;
    if (isFormatPremium && !canAccessPremiumFeatures()) {
      showToastMessage('This export format requires a premium subscription', 'error');
      return;
    }

    setIsExporting(true);
    setExportProgress(0);

    try {
      // Clone scene to avoid modifying the original
      const sceneToExport = scene.clone();

      // Apply export options
      if (exportOptions.optimizeGeometry) {
        // Optimize geometry (merge geometries, remove unused vertices, etc.)
        // This is a simplified example - in a real app, you'd have more sophisticated optimization
        sceneToExport.traverse((object) => {
  if (cachedData && !isExpired(cachedData)) {
  object.geometry.deleteAttribute('normal');
            object.geometry.deleteAttribute('uv');
            object.geometry.deleteAttribute('tangent');
            object.geometry.attributes.position.needsUpdate = true;
          }
        });
      }

      // Export based on format
      let result;
      let blob;
      let mimeType;
      let fileExtension = exportFormat;

      switch (exportFormat) {
      case 'glb':
        case 'gltf':
          const gltfExporter = new GLTFExporter();
          result = await new Promise((resolve, reject) => {
  gltfExporter.parse(
              sceneToExport,
              (result) => resolve(result),
              (error) => reject(error),
              {
    binary: exportFormat === 'glb',
                embedImages: exportOptions.includeTextures,
                animations: exportOptions.includeAnimations ? sceneToExport.animations : [],
                onlyVisible: true
              }
            );
          });

          mimeType = exportFormat === 'glb' ? 'application/octet-stream' : 'application/json';
          blob = new Blob([result], { type: mimeType });
          break;

        case 'obj':
          const objExporter = new OBJExporter();
          result = objExporter.parse(sceneToExport);
          blob = new Blob([result], { type: 'text/plain' });
          break;

        case 'stl':
          const stlExporter = new STLExporter();
          result = stlExporter.parse(sceneToExport, { binary: true });
          blob = new Blob([result], { type: 'application/octet-stream' });
          break;

        case 'dae':
          const colladaExporter = new ColladaExporter();
          result = colladaExporter.parse(sceneToExport);
          blob = new Blob([result.data], { type: 'text/xml' });
          break;

        case 'ply':
          const plyExporter = new PLYExporter();
          result = await new Promise((resolve, reject) => {
  plyExporter.parse(
              sceneToExport,
              (result) => resolve(result),
              { binary: true }
            );
          });
          blob = new Blob([result], { type: 'application/octet-stream' });
          break;

        case 'fbx':
          // FBXExporter is not available in three.js
          // This is a placeholder for future implementation
          showToastMessage('FBX export is not currently supported', 'error');
          setIsExporting(false);
          return;
          break;

        case 'png':
        case 'jpg':
          // Create a renderer to take a screenshot
          const renderer = new THREE.WebGLRenderer({ antialias: true, preserveDrawingBuffer: true });
          renderer.setSize(1920, 1080);
          renderer.setClearColor(0xffffff, 1);

          // Create camera
          const camera = new THREE.PerspectiveCamera(45, 1920 / 1080, 0.1, 1000);
          camera.position.set(0, 0, 5);

          // Create scene with the model
          const tempScene = new THREE.Scene();
          tempScene.add(sceneToExport);

          // Add lights
          const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
          tempScene.add(ambientLight);
          const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
          directionalLight.position.set(1, 1, 1);
          tempScene.add(directionalLight);

          // Render
          renderer.render(tempScene, camera);

          // Get image data
          const imgData = renderer.domElement.toDataURL(
            exportFormat === 'jpg' ? 'image/jpeg' : 'image/png',
            exportQuality === 'high' ? 1.0 : exportQuality === 'medium' ? 0.8 : 0.5
          );

          // Convert data URL to blob
          const byteString = atob(imgData.split(',')[1]);
          const mimeString = imgData.split(',')[0].split(':')[1].split(';')[0];
          const ab = new ArrayBuffer(byteString.length);
          const ia = new Uint8Array(ab);

          for (let i = 0; i < byteString.length; i++) {
            ia[i] = byteString.charCodeAt(i);
          }

          blob = new Blob([ab], { type: mimeString });
          break;

        case 'usdz':
          // USDZ export is not available in three.js
          // This is a placeholder for future implementation
          showToastMessage('USDZ export is not currently supported', 'error');
          setIsExporting(false);
          return;
          break;

        default:
          throw new Error(`Unsupported export format: ${exportFormat}`);
      }

      // Create download link
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${modelName}.${fileExtension}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      showToastMessage(`Model exported successfully as ${exportFormat.toUpperCase()}`, 'success');
    } catch (err) {
      showToastMessage(`Failed to export model: ${err.message}`, 'error');
    } finally {
      setIsExporting(false);
    }
  };

  // Render loading state
  if (cachedData && !isExpired(cachedData)) {
  return (
      <div className="flex items-center justify-center p-8 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
        <LoadingIndicator type="spinner" size="lg" text="Loading model for export..." />
      </div>
    );
  }

  // Render error state
  if (cachedData && !isExpired(cachedData)) {
  return (
      <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
        <h3 className="text-lg font-medium text-red-600 dark:text-red-400 mb-2">Error Loading Model</h3>
        <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
        <div className="flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      className="bg-white dark:bg-gray-800 rounded-lg shadow-xl overflow-hidden"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 20 }}
      transition={{ duration: 0.3 }}
    >
      <div className="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          Export Model
        </h2>
        <button
          onClick={onClose}
          className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          aria-label="Close"
        >
          <FiX />
        </button>
      </div>

      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Export Format</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              {formatOptions.map((format) => (
                <div
                  key={format.value}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
    exportFormat === format.value
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/30'
                      : 'border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700'
                  } ${format.premium && !canAccessPremiumFeatures() ? 'opacity-50' : ''}`}
                  onClick={() => {
  if (format.premium && !canAccessPremiumFeatures()) {
                      showToastMessage('This format requires a premium subscription', 'info');
                    } else {
                      setExportFormat(format.value);
                    }
                  }}
                >
                  <div className="flex items-center">
                    <div className="mr-3 text-blue-600 dark:text-blue-400">
                      {format.icon}
                    </div>
                    <div>
                      <div className="font-medium text-gray-900 dark:text-white flex items-center">
                        {format.label}
                        {format.premium && (
                          <span className="ml-2 text-xs bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 px-1.5 py-0.5 rounded">
                            Premium
                          </span>
                        )}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {format.description}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Export Options</h3>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Quality
              </label>
              <div className="grid grid-cols-3 gap-2">
                {qualityOptions.map((quality) => (
                  <button
                    key={quality.value}
                    type="button"
                    className={`py-2 px-3 text-sm font-medium rounded-md ${
    exportQuality === quality.value
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                    }`}
                    onClick={() => setExportQuality(quality.value)}
                  >
                    {quality.label}
                  </button>
                ))}
              </div>
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                {qualityOptions.find(q => q.value === exportQuality)?.description}
              </p>
            </div>

            <div className="space-y-3">
              <div className="flex items-center">
                <input
                  id="include-textures"
                  type="checkbox"
                  className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                  checked={exportOptions.includeTextures}
                  onChange={(e) => setExportOptions({ ...exportOptions, includeTextures: e.target.checked })}
                />
                <label htmlFor="include-textures" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                  Include textures
                </label>
              </div>

              <div className="flex items-center">
                <input
                  id="include-animations"
                  type="checkbox"
                  className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                  checked={exportOptions.includeAnimations}
                  onChange={(e) => setExportOptions({ ...exportOptions, includeAnimations: e.target.checked })}
                />
                <label htmlFor="include-animations" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                  Include animations
                </label>
              </div>

              <div className="flex items-center">
                <input
                  id="optimize-geometry"
                  type="checkbox"
                  className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                  checked={exportOptions.optimizeGeometry}
                  onChange={(e) => setExportOptions({ ...exportOptions, optimizeGeometry: e.target.checked })}
                />
                <label htmlFor="optimize-geometry" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                  Optimize geometry
                </label>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-6 flex justify-between items-center">
          <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center">
            <FiInfo className="mr-1" />
            {!currentUser ? (
              <span>Sign in to access all export formats</span>
            ) : !canAccessPremiumFeatures() && isPremiumModel ? (
              <span>Upgrade to premium to access all export formats for this model</span>
            ) : (
              <span>You have access to all export formats</span>
            )}
          </div>

          <div className="flex space-x-3">
            <button
              type="button"
              className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
              onClick={onClose}
            >
              Cancel
            </button>

            <button
              type="button"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={handleExport}
              disabled={isExporting}
            >
              {isExporting ? (
                <>
                  <LoadingIndicator type="spinner" size="sm" className="mr-2" />
                  Exporting...
                </>
              ) : (
                <>
                  <FiDownload className="mr-2" />
                  Export
                </>
              )}
            </button>
          </div>
        </div>

        {isExporting && (
          <div className="mt-4">
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
              <div
                className="bg-blue-600 h-2.5 rounded-full"
                style={{ width: `${exportProgress}%` }}
              ></div>
            </div>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1 text-center">
              {exportProgress}% complete
            </p>
          </div>
        )}
      </div>

      {/* Toast notifications */}
      {showToast && (
        <Toast
          type={toastType}
          message={toastMessage}
          visible={showToast}
          onClose={() => setShowToast(false)}
          duration={5000}
          position="bottom-right"
        />
      )}
    </motion.div>
  );
};

export default ModelExport;
