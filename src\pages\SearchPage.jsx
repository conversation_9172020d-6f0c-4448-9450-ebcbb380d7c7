import React, { useState, useEffect, useCallback } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FiSearch, FiFilter, FiGrid, FiList, FiCamera, FiSliders,
  FiStar, FiDownload, FiHeart, FiEye, FiChevronDown, FiX
} from 'react-icons/fi';
import { toast } from 'react-hot-toast';

import PageTransition from '../components/PageTransition';
import ImageSearch from '../components/search/ImageSearch';
import AdvancedSearch from '../components/search/AdvancedSearch';
import ModelCard from '../components/ModelCard';

const SearchPage = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();

  const [searchQuery, setSearchQuery] = useState(searchParams.get('q') || '');
  const [models, setModels] = useState([]);
  const [loading, setLoading] = useState(false);
  const [viewMode, setViewMode] = useState('grid');
  const [sortBy, setSortBy] = useState('relevance');
  const [showImageSearch, setShowImageSearch] = useState(false);
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [totalResults, setTotalResults] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);

  // Filters
  const [filters, setFilters] = useState({
    category: searchParams.get('category') || '',
    subcategory: searchParams.get('subcategory') || '',
    format: searchParams.get('format') || '',
    minRating: Number(searchParams.get('minRating')) || 0,
    isPremium: searchParams.get('isPremium') || '',
    sortBy: searchParams.get('sortBy') || 'relevance'
  });

  // Search function
  const performSearch = async (page = 1, resetResults = true) => {
  setLoading(true);

    try {
      const params = new URLSearchParams();

      if (searchQuery) params.append('q', searchQuery);
      if (filters.category) params.append('category', filters.category);
      if (filters.subcategory) params.append('subcategory', filters.subcategory);
      if (filters.format) params.append('format', filters.format);
      if (filters.minRating > 0) params.append('minRating', filters.minRating);
      if (filters.isPremium) params.append('isPremium', filters.isPremium);
      params.append('sortBy', sortBy);
      params.append('page', page);
      params.append('limit', 12);

      const response = await fetch(`http://localhost:5002/api/mongodb/models?${params.toString()}`);

      if (cachedData && !isExpired(cachedData)) {
  throw new Error('Search failed');
      }

      const data = await response.json();

      if (cachedData && !isExpired(cachedData)) {
  setModels(data.models || []);
      } else {
        setModels(prev => [...prev, ...(data.models || [])]);
      }

      setTotalResults(data.total || 0);
      setHasMore(data.hasMore || false);
      setCurrentPage(page);

    } catch (error) {
      toast.error('Search failed. Please try again.');
      setModels([]);
      setTotalResults(0);
    } finally {
      setLoading(false);
    }
  };

  // Load more results
  const loadMore = () => {
    if (cachedData && !isExpired(cachedData)) {
  performSearch(currentPage + 1, false);
    }
  };

  // Handle search input change
  const handleSearchChange = (e) => {
  setSearchQuery(e.target.value);
  };

  // Handle search submit
  const handleSearchSubmit = (e) => {
  e.preventDefault();
    updateURL();
    performSearch();
  };

  // Handle filter change
  const handleFilterChange = (key, value) => {
  setFilters(prev => ({ ...prev, [key]: value }));
  };

  // Handle sort change
  const handleSortChange = (newSortBy) => {
  setSortBy(newSortBy);
    updateURL({ sortBy: newSortBy });
    performSearch();
  };

  // Update URL with current search parameters
  const updateURL = (additionalParams = {}) => {
  const params = new URLSearchParams();

    if (searchQuery) params.append('q', searchQuery);
    if (filters.category) params.append('category', filters.category);
    if (filters.subcategory) params.append('subcategory', filters.subcategory);
    if (filters.format) params.append('format', filters.format);
    if (filters.minRating > 0) params.append('minRating', filters.minRating);
    if (filters.isPremium) params.append('isPremium', filters.isPremium);

    // Add additional params
    Object.entries(additionalParams).forEach(([key, value]) => {
  if (value) params.append(key, value);
    });

    params.append('sortBy', additionalParams.sortBy || sortBy);

    setSearchParams(params);
  };

  // Handle advanced search
  const handleAdvancedSearch = (searchData) => {
  setSearchQuery(searchData.query || '');
    setFilters({
    category: searchData.category || '',
      subcategory: searchData.subcategory || '',
      format: searchData.format || '',
      minRating: searchData.minRating || 0,
      isPremium: searchData.isPremium || '',
      sortBy: searchData.sortBy || 'relevance'
    });
    setSortBy(searchData.sortBy || 'relevance');
    setShowAdvancedSearch(false);

    // Update URL and search
    const params = new URLSearchParams();
    Object.entries(searchData).forEach(([key, value]) => {
  if (cachedData && !isExpired(cachedData)) {
  params.append(key, value);
      }
    });
    setSearchParams(params);
    performSearch();
  };

  // Apply filters
  const applyFilters = () => {
    updateURL();
    performSearch();
    setShowFilters(false);
  };

  // Clear filters
  const clearFilters = () => {
    setFilters({
    category: '',
      subcategory: '',
      format: '',
      minRating: 0,
      isPremium: '',
      sortBy: 'relevance'
    });
    setSortBy('relevance');
    setSearchParams(searchQuery ? { q: searchQuery } : {});
    performSearch();
  };

  // Initial search on component mount
  useEffect(() => {
  performSearch();
  }, []);

  // Update search when URL params change
  useEffect(() => {
    const query = searchParams.get('q') || '');
    setSearchQuery(query);

    setFilters({
    category: searchParams.get('category') || ',
      subcategory: searchParams.get('subcategory') || ',
      format: searchParams.get('format') || ',
      minRating: Number(searchParams.get('minRating')) || 0,
      isPremium: searchParams.get('isPremium') || ',
      sortBy: searchParams.get('sortBy') || 'relevance'
    });

    setSortBy(searchParams.get('sortBy') || 'relevance';
  }, [searchParams]);

  const sortOptions = [
    { value: 'relevance', label: 'Relevance' },
    { value: 'newest', label: 'Newest First' },
    { value: 'oldest', label: 'Oldest First' },
    { value: 'downloads', label: 'Most Downloaded' },
    { value: 'rating', label: 'Highest Rated' },
    { value: 'title', label: 'Alphabetical' }
  ];

  return (
    <div className="flex flex-col min-h-screen bg-gray-50 dark:bg-gray-900">
      <PageTransition>
        <main className="flex-grow pt-24 pb-16">
          <div className="container mx-auto px-4">
            {/* Search Header */}
            <div className="mb-8">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-center mb-6"
              >
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                  Search 3D Models
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                  Find the perfect 3D model for your project
                </p>
              </motion.div>

              {/* Search Bar */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="max-w-4xl mx-auto"
              >
                <form onSubmit={handleSearchSubmit} className="flex gap-3 mb-4">
                  <div className="flex-1 relative">
                    <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                      type="text"
                      value={searchQuery}
                      onChange={handleSearchChange}
                      placeholder="Search for models, scenes, objects..."
                      className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                  <button
                    type="submit"
                    className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Search
                  </button>
                </form>

                {/* Search Tools */}
                <div className="flex flex-wrap gap-3 justify-center">
                  <button
                    onClick={() => setShowImageSearch(true)}
                    className="flex items-center space-x-2 px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    <FiCamera className="h-4 w-4" />
                    <span>Visual Search</span>
                  </button>
                  <button
                    onClick={() => setShowAdvancedSearch(true)}
                    className="flex items-center space-x-2 px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    <FiSliders className="h-4 w-4" />
                    <span>Advanced Search</span>
                  </button>
                  <button
                    onClick={() => setShowFilters(!showFilters)}
                    className="flex items-center space-x-2 px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    <FiFilter className="h-4 w-4" />
                    <span>Filters</span>
                    <FiChevronDown className={`h-4 w-4 transition-transform ${showFilters ? 'rotate-180' : '}`} />
                  </button>
                </div>
              </motion.div>
            </div>

            {/* Filters Panel */}
            <AnimatePresence>
              {showFilters && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="mb-8 bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700"
                >
                  <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4 mb-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Category
                      </label>
                      <select
                        value={filters.category}
                        onChange={(e) => handleFilterChange('category', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                      >
                        <option value="">All Categories</option>
                        <option value="interior">Interior Scenes</option>
                        <option value="exterior">Exterior Scenes</option>
                        <option value="landscape">Landscape/Garden</option>
                        <option value="models">Models/Objects</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Format
                      </label>
                      <select
                        value={filters.format}
                        onChange={(e) => handleFilterChange('format', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                      >
                        <option value="">All Formats</option>
                        <option value="skp">SketchUp (.skp)</option>
                        <option value="obj">Wavefront (.obj)</option>
                        <option value="fbx">Autodesk FBX (.fbx)</option>
                        <option value="3ds">3D Studio (.3ds)</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Min Rating
                      </label>
                      <select
                        value={filters.minRating}
                        onChange={(e) => handleFilterChange('minRating', Number(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                      >
                        <option value={0}>Any Rating</option>
                        <option value={1}>1+ Stars</option>
                        <option value={2}>2+ Stars</option>
                        <option value={3}>3+ Stars</option>
                        <option value={4}>4+ Stars</option>
                        <option value={5}>5 Stars</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Type
                      </label>
                      <select
                        value={filters.isPremium}
                        onChange={(e) => handleFilterChange('isPremium', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                      >
                        <option value="">All Models</option>
                        <option value="false">Free Models</option>
                        <option value="true">Premium Models</option>
                      </select>
                    </div>

                    <div className="flex items-end space-x-2">
                      <button
                        onClick={applyFilters}
                        className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                      >
                        Apply
                      </button>
                      <button
                        onClick={clearFilters}
                        className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                      >
                        Clear
                      </button>
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Results Header */}
            {(models.length > 0 || loading) && (
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-4">
                  <h2 className="text-lg font-medium text-gray-900 dark:text-white">
                    {loading ? 'Searching...' : `${totalResults} results found`}
                  </h2>
                  {searchQuery && (
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      for "{searchQuery}"
                    </span>
                  )}
                </div>

                <div className="flex items-center space-x-4">
                  {/* Sort Dropdown */}
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-500 dark:text-gray-400">Sort by:</span>
                    <select
                      value={sortBy}
                      onChange={(e) => handleSortChange(e.target.value)}
                      className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white text-sm"
                    >
                      {sortOptions.map((option) => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* View Mode Toggle */}
                  <div className="flex border border-gray-300 dark:border-gray-600 rounded-md overflow-hidden">
                    <button
                      onClick={() => setViewMode('grid')}
                      className={`p-2 ${
    viewMode === 'grid'
                          ? 'bg-blue-600 text-white'
                          : 'bg-white dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600'
                      } transition-colors`}
                    >
                      <FiGrid className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => setViewMode('list')}
                      className={`p-2 ${
    viewMode === 'list'
                          ? 'bg-blue-600 text-white'
                          : 'bg-white dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600'
                      } transition-colors`}
                    >
                      <FiList className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Results Grid */}
            {loading && models.length === 0 ? (
              <div className="flex justify-center items-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
              </div>
            ) : models.length > 0 ? (
              <>
                <div className={`grid gap-6 mb-8 ${
    viewMode === 'grid'
                    ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
                    : 'grid-cols-1'
                }`}>
                  {models.map((model, index) => (
                    <motion.div
                      key={model._id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <ModelCard model={model} viewMode={viewMode} />
                    </motion.div>
                  ))}
                </div>

                {/* Load More Button */}
                {hasMore && (
                  <div className="text-center">
                    <button
                      onClick={loadMore}
                      disabled={loading}
                      className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      {loading ? 'Loading...' : 'Load More'}
                    </button>
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-12">
                <FiSearch className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-medium text-gray-900 dark:text-white mb-2">
                  No models found
                </h3>
                <p className="text-gray-500 dark:text-gray-400 mb-6">
                  Try adjusting your search terms or filters
                </p>
                <div className="flex justify-center space-x-4">
                  <button
                    onClick={() => setShowImageSearch(true)}
                    className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    <FiCamera className="h-4 w-4" />
                    <span>Try Visual Search</span>
                  </button>
                  <button
                    onClick={clearFilters}
                    className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    Clear All Filters
                  </button>
                </div>
              </div>
            )}
          </div>
        </main>
      </PageTransition>

      {/* Image Search Modal */}
      <ImageSearch
        isOpen={showImageSearch}
        onClose={() => setShowImageSearch(false)}
      />

      {/* Advanced Search Modal */}
      <AdvancedSearch
        isOpen={showAdvancedSearch}
        onClose={() => setShowAdvancedSearch(false)}
        onSearch={handleAdvancedSearch}
      />
    </div>
  );
};

export default SearchPage;
