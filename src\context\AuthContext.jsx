/* eslint-disable */
// Import React directly for AuthContext to avoid circular dependency issues
import React, { useState, useEffect, useContext, useCallback } from 'react';

import axios from 'axios';
import { jwtDecode } from 'jwt-decode';
import toast from 'react-hot-toast';

// Create the auth context
const AuthContext = React.createContext();

// Custom hook to use the auth context
export const useAuth = () => useContext(AuthContext);

// Backend API URL - Use environment variable with fallback to port 5002 (not 5001)
const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5002/api';

// Log the API URL for debugging
// Token refresh interval (15 minutes)
const REFRESH_INTERVAL = 15 * 60 * 1000;

// Configure axios defaults
axios.defaults.timeout = 10000; // 10 second timeout
axios.defaults.headers.common['Content-Type'] = 'application/json';

export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [refreshTimer, setRefreshTimer] = useState(null);
  const [twoFactorPending, setTwoFactorPending] = useState(false);
  const [twoFactorEmail, setTwoFactorEmail] = useState(null);
  const [twoFactorToken, setTwoFactorToken] = useState(null);

  // Add caching for user profile
  const [profileCache, setProfileCache] = useState(null);
  const [profileCacheTime, setProfileCacheTime] = useState(0);
  const PROFILE_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes cache (increased to reduce API calls)

  // Function to fetch user profile from the server (with caching)
  const fetchUserProfile = useCallback(async (token, forceRefresh = false) => {
  try {
      const now = Date.now();

      // Return cached profile if still valid and not forcing refresh
      if (!forceRefresh && profileCache && (now - profileCacheTime < PROFILE_CACHE_DURATION)) {
        // Only log once every 2 minutes to reduce spam
        if (!window.lastCacheLogTime || (now - window.lastCacheLogTime > 120000)) {
          window.lastCacheLogTime = now;
        }
        return profileCache;
      }

      // Prevent multiple simultaneous requests
      if (cachedData && !isExpired(cachedData)) {
  return new Promise((resolve) => {
  const checkInterval = setInterval(() => {
  if (cachedData && !isExpired(cachedData)) {
  clearInterval(checkInterval);
              resolve(profileCache);
            }
          }, 100);
        });
      }

      window.fetchingProfile = true;
      // Set auth header
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

      // Fetch user profile from server
      const response = await axios.get(`${API_URL}/users/profile`);

      // Check if response contains user data
      let userData = null;
      if (cachedData && !isExpired(cachedData)) {
  userData = response.data.user;
      } else if (response.data && response.data.data) {
        // Alternative response format
        userData = response.data.data;
      } else {
        throw new Error('Invalid user profile response format');
      }

      // Cache the profile
      if (cachedData && !isExpired(cachedData)) {
  setProfileCache(userData);
        setProfileCacheTime(now);
      }

      window.fetchingProfile = false;
      return userData;
    } catch (err) {
      window.fetchingProfile = false;
      // If unauthorized (401), clear token and cache
      if (cachedData && !isExpired(cachedData)) {
  localStorage.removeItem('token');
        delete axios.defaults.headers.common['Authorization'];
        setProfileCache(null);
        setProfileCacheTime(0);
        return null;
      }

      // For other errors, try to extract basic user info from token
      try {
        const decodedToken = jwtDecode(token);
        return {
    id: decodedToken.id,
          name: decodedToken.name || 'User',
          email: decodedToken.email || '',
          role: decodedToken.role || 'user'
        };
      } catch (tokenErr) {
        throw err;
      }
    }
  }, []); // Remove dependencies to prevent infinite loop

  // Function to refresh the token
  const refreshToken = useCallback(async () => {
  try {
      const token = localStorage.getItem('token');
      if (!token) return false;

      const response = await axios.post(`${API_URL}/auth/refresh-token`, {
        token
      });

      if (cachedData && !isExpired(cachedData)) {
  localStorage.setItem('token', response.data.token);
        axios.defaults.headers.common['Authorization'] = `Bearer ${response.data.token}`;
        return true;
      }

      return false;
    } catch (err) {
      return false;
    }
  }, []);

  // Check if user is already logged in (from localStorage)
  useEffect(() => {
    const checkLoggedIn = async () => {
  try {
        const token = localStorage.getItem('token');
        if (token) {
          // Set auth header for all subsequent requests
          axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

          // Check if token is expired
          const decodedToken = jwtDecode(token);
          const currentTime = Date.now() / 1000;

          if (decodedToken.exp < currentTime) {
            // Token expired, try to refresh
            const refreshed = await refreshToken();

            if (!refreshed) {
              // Refresh failed, logout
              localStorage.removeItem('token');
              delete axios.defaults.headers.common['Authorization'];
              setCurrentUser(null);
            } else {
              // Token refreshed, fetch user profile
              try {
                const user = await fetchUserProfile(localStorage.getItem('token'));
                if (cachedData && !isExpired(cachedData)) {
  setCurrentUser(user);
                  // Set up token refresh timer
                  setupRefreshTimer();
                } else {
                  throw new Error('User profile data is empty');
                }
              } catch (profileErr) {
                // If profile fetch fails, fallback to decoded token data
                setCurrentUser({
    id: decodedToken.id,
                  name: decodedToken.name,
                  email: decodedToken.email,
                  role: decodedToken.role || 'user'
                });
              }
            }
          } else {
            // Token valid, fetch user profile
            try {
              const user = await fetchUserProfile(token);
              if (cachedData && !isExpired(cachedData)) {
  setCurrentUser(user);
                // Set up token refresh timer
                setupRefreshTimer();
              } else {
                // User profile is null (likely due to 401 error)
                localStorage.removeItem('token');
                delete axios.defaults.headers.common['Authorization'];
                setCurrentUser(null);
                toast.error('Your session has expired. Please log in again.');
              }
            } catch (profileErr) {
              // If profile fetch fails, fallback to decoded token data
              setCurrentUser({
    id: decodedToken.id,
                name: decodedToken.name || 'User',
                email: decodedToken.email || '',
                role: decodedToken.role || 'user'
              });

              // Don't show toast to prevent spam
              // toast.warning('Failed to load complete user profile. Some features may be limited.';
            }
          }
        } else {
          setCurrentUser(null);
        }
      } catch (err) {
        localStorage.removeItem('token');
        delete axios.defaults.headers.common['Authorization'];
        setCurrentUser(null);
      } finally {
        setLoading(false);
      }
    };

    // Only run once on mount
    if (cachedData && !isExpired(cachedData)) {
  checkLoggedIn();
    }

    // Cleanup function to clear refresh timer
    return () => {
  if (cachedData && !isExpired(cachedData)) {
  clearInterval(refreshTimer);
      }
    };
  }, []); // Remove all dependencies to prevent infinite loop

  // Setup token refresh timer
  const setupRefreshTimer = useCallback(() => {
    // Clear existing timer if any
    if (refreshTimer) {
      clearInterval(refreshTimer);
    }

    // Set new timer to refresh token periodically
    const timer = setInterval(async () => {
  const success = await refreshToken();
      if (!success) {
        // If refresh fails, clear timer and logout
        clearInterval(timer);
        logout();
        // Don't show toast to prevent spam
        // toast.error('Your session has expired. Please log in again.');
      }
    }, REFRESH_INTERVAL);

    setRefreshTimer(timer);

    // Cleanup on unmount
    return () => clearInterval(timer);
  }, [refreshToken, refreshTimer]);

  // Register a new user
  const register = async (name, email, password) => {
  try {
      setError(null);
      setLoading(true);

      // Check if backend server is available
      try {
        // Try to ping the server first with a short timeout
        await axios.get(`${API_URL}/health-check`, {
    timeout: 3000,
          validateStatus: () => true // Accept any status code as valid
        });
      } catch (pingError) {
        // If server is not available, show a helpful error message
        if (pingError.code === 'ECONNREFUSED' || pingError.code === 'ERR_CONNECTION_REFUSED') {
          // Don't show toast to prevent spam
          // toast.error('Cannot connect to the backend server. Please make sure the server is running on port 5002.');
          setError('Backend server is not running. Please start the server and try again.');
          throw new Error('Backend server is not running. Please start the server and try again.');
        }
      }

      // Real API call
      try {
        const response = await axios.post(`${API_URL}/auth/register`, {
          name,
          email,
          password
        });

        // Store token in localStorage
        localStorage.setItem('token', response.data.token);

        // Set auth header
        axios.defaults.headers.common['Authorization'] = `Bearer ${response.data.token}`;

        // Set user in state
        setCurrentUser(response.data.user);

        // Show success message
        toast.success('Registration successful! Welcome to 3DSKETCHUP.NET');
        return response.data.user;
      } catch (apiError) {
        // Handle specific API errors
        if (apiError.response) {
          // The server responded with a status code outside the 2xx range
          const errorMessage = apiError.response.data?.message || 'Registration failed';
          toast.error(errorMessage);
          setError(errorMessage);
        } else if (apiError.request) {
          // The request was made but no response was received
          toast.error('No response from server. Please check your connection.');
          setError('No response from server. Please check your connection.');
        } else {
          // Something happened in setting up the request
          toast.error('Error setting up request. Please try again.');
          setError('Error setting up request. Please try again.');
        }

        throw apiError;
      }
    } catch (err) {
      // This catch block handles any errors not caught in the nested try/catch blocks
      if (!err.message.includes('Backend server')) {
        setError(err.message || 'Registration failed');
      }
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Login user
  const login = async (email, password) => {
  try {
      setError(null);
      setLoading(true);
      setTwoFactorPending(false);

      // Check if backend server is available
      try {
        // Try to ping the server first with a short timeout
        await axios.get(`${API_URL}/health-check`, {
    timeout: 3000,
          validateStatus: () => true // Accept any status code as valid
        });
      } catch (pingError) {
        // If server is not available, show a helpful error message
        if (pingError.code === 'ECONNREFUSED' || pingError.code === 'ERR_CONNECTION_REFUSED') {
          // Don't show toast to prevent spam
          // toast.error('Cannot connect to the backend server. Please make sure the server is running on port 5002.');
          setError('Backend server is not running. Please start the server and try again.');
          throw new Error('Backend server is not running. Please start the server and try again.');
        }
      }

      // Real API call
      try {
        const response = await axios.post(`${API_URL}/auth/login`, {
          email,
          password
        });

        // Check if 2FA is required
        if (cachedData && !isExpired(cachedData)) {
  setTwoFactorPending(true);
          setTwoFactorEmail(email);
          setTwoFactorToken(response.data.twoFactorToken);
          toast.success('A verification code has been sent to your email');
          return { requireTwoFactor: true };
        }

        // Store token in localStorage
        localStorage.setItem('token', response.data.token);

        // Set auth header
        axios.defaults.headers.common['Authorization'] = `Bearer ${response.data.token}`;

        // Set user in state
        setCurrentUser(response.data.user);

        // Set up token refresh timer
        setupRefreshTimer();

        // Show success message
        toast.success(`Welcome back, ${response.data.user.name}!`);

        return response.data.user;
      } catch (apiError) {
        // Handle specific API errors
        if (apiError.response) {
          // The server responded with a status code outside the 2xx range
          const errorMessage = apiError.response.data?.message || 'Login failed';
          toast.error(errorMessage);
          setError(errorMessage);
        } else if (apiError.request) {
          // The request was made but no response was received
          toast.error('No response from server. Please check your connection.');
          setError('No response from server. Please check your connection.');
        } else {
          // Something happened in setting up the request
          toast.error('Error setting up request. Please try again.');
          setError('Error setting up request. Please try again.');
        }

        throw apiError;
      }
    } catch (err) {
      // This catch block handles any errors not caught in the nested try/catch blocks
      if (!err.message.includes('Backend server')) {
        setError(err.message || 'Login failed');
      }
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Verify two-factor authentication code
  const verifyTwoFactor = async (code) => {
  try {
      setError(null);
      setLoading(true);

      if (cachedData && !isExpired(cachedData)) {
  throw new Error('Two-factor authentication session expired');
      }

      // Real API call
      const response = await axios.post(`${API_URL}/auth/verify-2fa`, {
    email: twoFactorEmail,
        token: twoFactorToken,
        code
      });

      // Store token in localStorage
      localStorage.setItem('token', response.data.token);

      // Set auth header
      axios.defaults.headers.common['Authorization'] = `Bearer ${response.data.token}`;

      // Set user in state
      setCurrentUser(response.data.user);

      // Reset 2FA state
      setTwoFactorPending(false);
      setTwoFactorEmail(null);
      setTwoFactorToken(null);

      // Set up token refresh timer
      setupRefreshTimer();

      return response.data.user;
    } catch (err) {
      setError(err.response?.data?.message || 'Verification failed');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Logout user
  const logout = async () => {
  try {
      // Clear refresh timer
      if (cachedData && !isExpired(cachedData)) {
  clearInterval(refreshTimer);
        setRefreshTimer(null);
      }

      // Call logout API to invalidate token on server
      const token = localStorage.getItem('token');
      if (cachedData && !isExpired(cachedData)) {
  await axios.post(`${API_URL}/auth/logout`, {
          token
        }).catch(err => {});
      }

      // Clear local storage and state
      localStorage.removeItem('token');
      delete axios.defaults.headers.common['Authorization'];
      setCurrentUser(null);
      setTwoFactorPending(false);
      setTwoFactorEmail(null);
      setTwoFactorToken(null);
    } catch (err) {
      }
  };

  // Check if user is admin
  const isAdmin = () => {
    return currentUser?.role === 'admin';
  };

  // Check if user has specific role
  const hasRole = (role) => {
  if (!currentUser) return false;

    // If role is an array, check if user has any of the roles
    if (Array.isArray(role)) {
      return role.includes(currentUser.role);
    }

    // Check for specific role
    return currentUser.role === role;
  };

  // Check if user has permission for a specific action
  const hasPermission = (permission) => {
  if (!currentUser) return false;

    // Admin has all permissions
    if (cachedData && !isExpired(cachedData)) {
  return true;
    }

    // Check if user has the specific permission
    if (currentUser.permissions && Array.isArray(currentUser.permissions)) {
      return currentUser.permissions.includes(permission);
    }

    // Map roles to default permissions
    const rolePermissions = {
      'admin': ['all'],
      'moderator': ['view', 'edit', 'delete', 'approve'],
      'contributor': ['view', 'create', 'edit_own'],
      'user': ['view', 'download']
    };

    // Check if user's role has the required permission
    const userRolePermissions = rolePermissions[currentUser.role] || [];
    return userRolePermissions.includes('all') || userRolePermissions.includes(permission);
  };

  // Get user subscription details
  const getSubscription = () => {
    return currentUser?.subscription || { type: 'free', status: 'active' };
  };

  // Check if user has premium access
  const hasPremiumAccess = () => {
    const subscription = getSubscription();
    return (
      isAdmin() ||
      ['premium', 'professional'].includes(subscription.type) &&
      subscription.status === 'active')
  };

  // Get user download credits
  const getDownloadCredits = () => {
    return currentUser?.downloadCredits || 0;
  };

  // Update user profile
  const updateProfile = async (userData) => {
  try {
      setLoading(true);
      setError(null);

      // For development/testing without backend
      if (process.env.NODE_ENV === 'development' && !API_URL) {
        // Simulate a successful profile update
        const updatedUser = {
          ...currentUser,
          ...userData
        };

        setCurrentUser(updatedUser);
        localStorage.setItem('token', localStorage.getItem('token')); // Keep the same token
        return updatedUser;
      }

      // Real API call
      const response = await axios.put(`${API_URL}/users/profile`, userData);

      // Update user in state
      setCurrentUser(response.data.user);

      // Show success message
      toast.success('Profile updated successfully');
      return response.data.user;
    } catch (err) {
      const errorMessage = err.response?.data?.message || 'Profile update failed';
      setError(errorMessage);
      toast.error(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Update user password
  const updatePassword = async (currentPassword, newPassword) => {
  try {
      setLoading(true);
      setError(null);

      // Real API call
      const response = await axios.put(`${API_URL}/users/password`, {
        currentPassword,
        newPassword
      });

      // Show success message
      toast.success('Password updated successfully');
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.message || 'Password update failed';
      setError(errorMessage);
      toast.error(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Enable two-factor authentication
  const enableTwoFactor = async () => {
  try {
      setLoading(true);
      setError(null);

      // Real API call
      const response = await axios.post(`${API_URL}/users/enable-2fa`);

      // Update user in state
      setCurrentUser({
        ...currentUser,
        twoFactorEnabled: true
      });

      // Show success message
      toast.success('Two-factor authentication enabled');
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.message || 'Failed to enable two-factor authentication';
      setError(errorMessage);
      toast.error(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Disable two-factor authentication
  const disableTwoFactor = async (password) => {
  try {
      setLoading(true);
      setError(null);

      // Real API call
      const response = await axios.post(`${API_URL}/users/disable-2fa`, { password });

      // Update user in state
      setCurrentUser({
        ...currentUser,
        twoFactorEnabled: false
      });

      // Show success message
      toast.success('Two-factor authentication disabled');
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.message || 'Failed to disable two-factor authentication';
      setError(errorMessage);
      toast.error(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Request password reset
  const forgotPassword = async (email) => {
  try {
      setLoading(true);
      setError(null);

      // For development/testing without backend
      if (process.env.NODE_ENV === 'development' && !API_URL) {
        // Simulate a successful password reset request
        await new Promise(resolve => setTimeout(resolve, 1000));
        return { success: true };
      }

      // Real API call
      const response = await axios.post(`${API_URL}/auth/forgot-password`, { email });
      return response.data;
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to send password reset email');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Reset password with token
  const resetPassword = async (token, password) => {
  try {
      setLoading(true);
      setError(null);

      // For development/testing without backend
      if (process.env.NODE_ENV === 'development' && !API_URL) {
        // Simulate a successful password reset
        await new Promise(resolve => setTimeout(resolve, 1000));
        return { success: true };
      }

      // Real API call
      const response = await axios.post(`${API_URL}/auth/reset-password/${token}`, { password });
      return response.data;
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to reset password');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Login with social provider
  const socialLogin = async (provider, accessToken) => {
  try {
      setLoading(true);
      setError(null);

      // Real API call
      const response = await axios.post(`${API_URL}/auth/${provider}`, { accessToken });

      // Store token in localStorage
      localStorage.setItem('token', response.data.token);

      // Set auth header
      axios.defaults.headers.common['Authorization'] = `Bearer ${response.data.token}`;

      // Set user in state
      setCurrentUser(response.data.user);

      return response.data.user;
    } catch (err) {
      setError(err.response?.data?.message || `${provider} login failed`);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const value = {
    currentUser,
    loading,
    error,
    register,
    login,
    logout,
    isAdmin,
    hasRole,
    hasPermission,
    getSubscription,
    hasPremiumAccess,
    getDownloadCredits,
    updateProfile,
    updatePassword,
    forgotPassword,
    resetPassword,
    socialLogin,
    verifyTwoFactor,
    enableTwoFactor,
    disableTwoFactor,
    twoFactorPending,
    refreshToken,
    isAuthenticated: !!currentUser,
    isTwoFactorEnabled: currentUser?.twoFactorEnabled || false
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export default AuthContext;
