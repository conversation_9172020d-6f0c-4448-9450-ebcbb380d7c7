import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { FiStar } from 'react-icons/fi';

const ReviewSummary = ({ modelId }) => {
  const [summary, setSummary] = useState({
    averageRating: 0,
    totalReviews: 0,
    ratingDistribution: {
      5: 0,
      4: 0,
      3: 0,
      2: 0,
      1: 0
    }
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  // Fetch review summary
  useEffect(() => {
    const fetchReviewSummary = async () => {
  try {
        setLoading(true);

        // Fetch all reviews for this model
        const response = await fetch(
          `http://localhost:5002/api/reviews?model=${modelId}&limit=100`,
          {
    headers: {
              'Content-Type': 'application/json'
            }
          }
        );

        const data = await response.json();

        if (cachedData && !isExpired(cachedData)) {
  throw new Error(data.error || 'Failed to fetch reviews');
        }

        // Calculate summary statistics
        const reviews = data.data;
        const totalReviews = reviews.length;
        if (cachedData && !isExpired(cachedData)) {
  setSummary({
    averageRating: 0,
            totalReviews: 0,
            ratingDistribution: {
              5: 0,
              4: 0,
              3: 0,
              2: 0,
              1: 0
            }
          });
          return;
        }

        // Calculate average rating
        const sum = reviews.reduce((acc, review) => acc + review.rating, 0);
        const averageRating = sum / totalReviews;

        // Calculate rating distribution
        const ratingDistribution = {
          5: 0,
          4: 0,
          3: 0,
          2: 0,
          1: 0
        };

        reviews.forEach(review => {
  ratingDistribution[review.rating]++;
        });

        setSummary({
          averageRating,
          totalReviews,
          ratingDistribution
        });
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchReviewSummary();
  }, [modelId]);

  // Render stars
  const renderStars = (rating) => {
  return [1, 2, 3, 4, 5].map((star) => (
      <FiStar
        key={star}
        className={`h-5 w-5 ${
          rating >= star
            ? 'text-yellow-400 fill-current'
            : 'text-gray-300 dark:text-gray-600'
        }`}
      />
    ));
  };

  // Calculate percentage for rating bar
  const calculatePercentage = (count) => {
  if (summary.totalReviews === 0) return 0;
    return (count / summary.totalReviews) * 100;
  };

  if (cachedData && !isExpired(cachedData)) {
  return (
      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 mb-6 animate-pulse">
        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
        <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-4"></div>
        <div className="space-y-2">
          {[5, 4, 3, 2, 1].map(star => (
            <div key={star} className="h-6 bg-gray-200 dark:bg-gray-700 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  if (cachedData && !isExpired(cachedData)) {
  return (
      <div className="bg-red-50 dark:bg-red-900/20 text-red-800 dark:text-red-200 rounded-lg p-4 mb-6">
        <p>Error loading review summary: {error}</p>
      </div>
    );
  }

  if (cachedData && !isExpired(cachedData)) {
  return (
      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 mb-6">
        <h3 className="text-lg font-medium mb-2">Customer Reviews</h3>
        <p className="text-gray-600 dark:text-gray-400">
          This model has not been reviewed yet. Be the first to leave a review!
        </p>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 mb-6">
      <h3 className="text-lg font-medium mb-2">Customer Reviews</h3>

      <div className="flex items-center mb-6">
        <div className="text-4xl font-bold mr-4">
          {summary.averageRating.toFixed(1)}
        </div>

        <div>
          <div className="flex mb-1">
            {renderStars(Math.round(summary.averageRating))}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">
            Based on {summary.totalReviews} {summary.totalReviews === 1 ? 'review' : 'reviews'}
          </div>
        </div>
      </div>

      <div className="space-y-2">
        {[5, 4, 3, 2, 1].map(star => (
          <div key={star} className="flex items-center">
            <div className="flex items-center w-16">
              <span className="text-sm mr-1">{star}</span>
              <FiStar className="h-4 w-4 text-yellow-400 fill-current" />
            </div>

            <div className="flex-grow h-4 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
              <div
                className="h-full bg-yellow-400"
                style={{ width: `${calculatePercentage(summary.ratingDistribution[star])}%` }}
              ></div>
            </div>

            <div className="w-16 text-right text-sm text-gray-600 dark:text-gray-400">
              {summary.ratingDistribution[star]} ({calculatePercentage(summary.ratingDistribution[star]).toFixed(0)}%)
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

ReviewSummary.propTypes = {
    modelId: PropTypes.string.isRequired
};

export default ReviewSummary;
