import axios from 'axios';
import {
  setCacheItem,
  getCacheItem,
  removeCacheItem,
  invalidateCachePattern
} from './cache';

// Default cache TTL (5 minutes)
const DEFAULT_CACHE_TTL = 5 * 60 * 1000;

// Create an axios instance
const api = axios.create({
    baseURL: import.meta.env.VITE_API_URL || 'http://localhost:5002/api',
  headers: {
    'Content-Type': 'application/json'
  },
  timeout: 60000 // 60 seconds timeout to prevent timeout errors
});

// Add a request interceptor to add the auth token to every request
api.interceptors.request.use(
  (config) => {
    // Check if we should use cache for this request
    if (cachedData && !isExpired(cachedData)) {
  const cacheKey = generateCacheKey(config);
      const cachedResponse = getCacheItem(cacheKey);

      if (cachedResponse) {
        // Return a promise that resolves with the cached response
        // This will be caught by the adapter
        config.adapter = () => {
  return Promise.resolve({
    data: cachedResponse,
            status: 200,
            statusText: 'OK',
            headers: {},
            config,
            request: {}
          });
        };
      }
    }

    // Add auth token
    const token = localStorage.getItem('token');
    if (cachedData && !isExpired(cachedData)) {
  config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error) => Promise.reject(error)
);

// Add a response interceptor to handle token expiration and cache responses
api.interceptors.response.use(
  (response) => {
    // Cache successful GET responses
    if (cachedData && !isExpired(cachedData)) {
  const cacheKey = generateCacheKey(response.config);
      const ttl = response.config.cacheTTL || DEFAULT_CACHE_TTL;
      setCacheItem(cacheKey, response.data, ttl);
    }

    return response;
  },
  (error) => {
    // Handle 401 Unauthorized errors (token expired)
    if (cachedData && !isExpired(cachedData)) {
  localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Generate a cache key from a request config
const generateCacheKey = (config) => {
  const { url, params, data } = config;
  const queryParams = params ? JSON.stringify(params) : '';
  const bodyData = data ? JSON.stringify(data) : '';
  return `${url}:${queryParams}:${bodyData}`;
};

// Auth API calls
export const authAPI = {
    register: (userData) => api.post('/auth/register', userData),
  login: (userData) => api.post('/auth/login', userData),
  getMe: () => api.get('/auth/me'),
  logout: () => api.get('/auth/logout')
};

// Models API calls
export const modelsAPI = {
    getModels: (params) => {
  try {
      // Use real API call with caching (10 minutes TTL)
      return api.get('/models', {
        params,
        cacheTTL: 10 * 60 * 1000
      });
    } catch (error) {
      throw error;
    }
  },

  getModel: (id) => {
  try {
      // Use real API call with caching (15 minutes TTL)
      return api.get(`/models/${id}`, {
    cacheTTL: 15 * 60 * 1000
      });
    } catch (error) {
      throw error;
    }
  },

  createModel: (modelData) => {
    // Use real API call
    const response = api.post('/models', modelData);
    // Invalidate models cache after creating a new model
    invalidateCachePattern(/^\/models/);
    return response;
  },

  updateModel: (id, modelData) => {
    // Use real API call
    const response = api.put(`/models/${id}`, modelData);
    // Invalidate specific model cache and models list cache
    removeCacheItem(`/models/${id}`);
    invalidateCachePattern(/^\/models\?/);
    return response;
  },

  deleteModel: (id) => {
    // Use real API call
    const response = api.delete(`/models/${id}`);
    // Invalidate specific model cache and models list cache
    removeCacheItem(`/models/${id}`);
    invalidateCachePattern(/^\/models\?/);
    return response;
  },

  downloadModel: (id) => {
    // Use real API call without caching for downloads
    return api.get(`/models/${id}/download`, { cache: false });
  },

  getFeaturedModels: () => {
  try {
      // Use real API call with longer caching (30 minutes TTL)
      return api.get('/models/featured', {
    cacheTTL: 30 * 60 * 1000
      });
    } catch (error) {
      throw error;
    }
  },

  getRelatedModels: (id) => {
  try {
      // Use real API call with caching (15 minutes TTL)
      return api.get(`/models/${id}/related`, {
    cacheTTL: 15 * 60 * 1000
      });
    } catch (error) {
      throw error;
    }
  },

  searchModels: (query, params) => {
  try {
      // Use real API call with shorter caching (5 minutes TTL)
      return api.get('/models/search', {
    params: { query, ...params },
        cacheTTL: 5 * 60 * 1000
      });
    } catch (error) {
      throw error;
    }
  }
};

// User API calls
export const userAPI = {
  // Profile
  getUserProfile: () => api.get('/users/profile'),
  updateUserProfile: (userData) => api.put('/users/profile', userData),
  updatePassword: (passwordData) => api.put('/users/password', passwordData),

  // Activity
  getUserActivity: () => api.get('/users/activity'),

  // Saved models
  getSavedModels: () => api.get('/users/saved-models'),
  saveModel: (id) => api.post(`/users/saved-models/${id}`),
  removeSavedModel: (id) => api.delete(`/users/saved-models/${id}`),

  // Downloads
  getDownloadHistory: () => api.get('/users/download-history'),

  // Subscription
  getUserSubscription: () => api.get('/users/subscription'),
  updateSubscription: (subscriptionData) => api.put('/users/subscription', subscriptionData),
  cancelSubscription: () => api.post('/users/subscription/cancel'),
  reactivateSubscription: () => api.post('/users/subscription/reactivate'),

  // Security
  getUserSecurity: () => api.get('/users/security'),
  enable2FA: () => api.post('/users/security/2fa/enable'),
  verify2FA: (verificationData) => api.post('/users/security/2fa/verify', verificationData),
  disable2FA: () => api.post('/users/security/2fa/disable')
};

// Stats API calls
export const statsAPI = {
    getSiteStats: () => api.get('/stats', {
    // Cache site stats for 5 minutes
    cacheTTL: 5 * 60 * 1000
  }),
  getAdminStats: () => api.get('/stats/admin', {
    // Cache admin stats for 2 minutes
    cacheTTL: 2 * 60 * 1000
  }),
  getModelStats: (id) => api.get(`/stats/models/${id}`, {
    // Cache model stats for 10 minutes
    cacheTTL: 10 * 60 * 1000
  }),
  getUserStats: () => api.get('/stats/user', {
    // Cache user stats for 5 minutes
    cacheTTL: 5 * 60 * 1000
  }),
  // Invalidate stats cache
  invalidateStatsCache: () => {
  invalidateCachePattern(/^\/stats/);
  }
};

// Admin API calls
export const adminAPI = {
  // User Management
  getUsers: (params) => {
  try {
      return api.get('/users', { params });
    } catch (error) {
      throw error;
    }
  },

  getUser: (id) => {
  return api.get(`/users/${id}`);
  },

  createUser: (userData) => {
  return api.post('/users', userData);
  },

  updateUser: (id, userData) => {
  return api.put(`/users/${id}`, userData);
  },

  deleteUser: (id) => {
  return api.delete(`/users/${id}`);
  },

  bulkDeleteUsers: (userIds) => {
  return api.post('/users/bulk-delete', { userIds });
  },

  // Model Management
  getAdminModels: (params) => {
  return api.get('/admin/models', { params });
  },

  createModel: (modelData) => {
  return api.post('/models', modelData);
  },
  createUser: (userData) => {
  return api.post('/admin/users', userData);
  },

  approveModel: (id) => {
  return api.put(`/admin/models/${id}/approve`);
  },

  rejectModel: (id, reason) => {
  return api.put(`/admin/models/${id}/reject`, { reason });
  },

  featureModel: (id) => {
  return api.put(`/admin/models/${id}/feature`);
  },

  unfeatureModel: (id) => {
  return api.put(`/admin/models/${id}/unfeature`);
  },

  bulkDeleteModels: (modelIds) => {
  return api.post('/admin/models/bulk-delete', { modelIds });
  },

  bulkUpdateModels: (modelIds, updateData) => {
  return api.put('/admin/models/bulk-update', { modelIds, updateData });
  },

  // Analytics
  getAnalytics: (timeRange = '30d') => {
  return api.get('/admin/analytics', { params: { timeRange } });
  },

  getUserAnalytics: (timeRange = '30d') => {
  return api.get('/admin/analytics/users', { params: { timeRange } });
  },

  getModelAnalytics: (timeRange = '30d') => {
  return api.get('/admin/analytics/models', { params: { timeRange } });
  },

  getRevenueAnalytics: (timeRange = '30d') => {
  return api.get('/admin/analytics/revenue', { params: { timeRange } });
  },

  // Settings
  getSettings: () => {
  return api.get('/admin/settings');
  },

  updateSettings: (settings) => {
  return api.put('/admin/settings', settings);
  },

  // System
  getSystemInfo: () => {
  return api.get('/admin/system');
  },

  clearCache: () => {
  return api.post('/admin/system/clear-cache');
  },

  backupDatabase: () => {
  return api.post('/admin/system/backup');
  },

  // Reports
  generateUserReport: (params) => {
  return api.get('/admin/reports/users', { params });
  },

  generateModelReport: (params) => {
  return api.get('/admin/reports/models', { params });
  },

  generateRevenueReport: (params) => {
  return api.get('/admin/reports/revenue', { params });
  }
};

export default api;
